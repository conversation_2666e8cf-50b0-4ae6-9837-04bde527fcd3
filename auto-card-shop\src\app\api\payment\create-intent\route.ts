import { NextRequest, NextResponse } from 'next/server'
import { stripe } from '@/lib/stripe'
import { prisma } from '@/lib/prisma'

export async function POST(request: NextRequest) {
  try {
    const { items, email } = await request.json()

    if (!items || !Array.isArray(items) || items.length === 0) {
      return NextResponse.json(
        { error: '购物车不能为空' },
        { status: 400 }
      )
    }

    if (!email) {
      return NextResponse.json(
        { error: '邮箱地址是必需的' },
        { status: 400 }
      )
    }

    // 验证商品并计算总价
    let totalAmount = 0
    const orderItems = []

    for (const item of items) {
      const product = await prisma.product.findUnique({
        where: { id: item.productId },
        include: {
          _count: {
            select: {
              cards: {
                where: { status: 'AVAILABLE' }
              }
            }
          }
        }
      })

      if (!product) {
        return NextResponse.json(
          { error: `商品不存在: ${item.productId}` },
          { status: 404 }
        )
      }

      if (product.status !== 'ACTIVE') {
        return NextResponse.json(
          { error: `商品已下架: ${product.name}` },
          { status: 400 }
        )
      }

      if (product._count.cards < item.quantity) {
        return NextResponse.json(
          { error: `库存不足: ${product.name}` },
          { status: 400 }
        )
      }

      const itemTotal = product.price * item.quantity
      totalAmount += itemTotal

      orderItems.push({
        productId: product.id,
        quantity: item.quantity,
        price: product.price,
      })
    }

    // 创建订单
    const order = await prisma.order.create({
      data: {
        email,
        totalAmount,
        status: 'PENDING',
        orderItems: {
          create: orderItems
        }
      },
      include: {
        orderItems: {
          include: {
            product: true
          }
        }
      }
    })

    // 创建 Stripe PaymentIntent
    const paymentIntent = await stripe.paymentIntents.create({
      amount: Math.round(totalAmount * 100), // Stripe 使用分为单位
      currency: 'usd',
      metadata: {
        orderId: order.id,
        email: email,
      },
    })

    // 更新订单的 Stripe 支付 ID
    await prisma.order.update({
      where: { id: order.id },
      data: { stripePaymentId: paymentIntent.id }
    })

    return NextResponse.json({
      clientSecret: paymentIntent.client_secret,
      orderId: order.id,
    })
  } catch (error) {
    console.error('创建支付意图错误:', error)
    return NextResponse.json(
      { error: '服务器错误' },
      { status: 500 }
    )
  }
}
