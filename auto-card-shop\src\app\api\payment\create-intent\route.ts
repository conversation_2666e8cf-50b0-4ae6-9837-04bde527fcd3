import { NextRequest, NextResponse } from 'next/server'
import { stripe } from '@/lib/stripe'
import { prisma } from '@/lib/prisma'

export async function POST(request: NextRequest) {
  try {
    const { items, email } = await request.json()

    if (!items || !Array.isArray(items) || items.length === 0) {
      return NextResponse.json(
        { error: '购物车不能为空' },
        { status: 400 }
      )
    }

    if (!email) {
      return NextResponse.json(
        { error: '邮箱地址是必需的' },
        { status: 400 }
      )
    }

    // 验证商品并计算总价
    let totalAmount = 0
    const orderItems = []

    for (const item of items) {
      const product = await prisma.product.findUnique({
        where: { id: item.productId },
        include: {
          _count: {
            select: {
              cards: {
                where: { status: 'AVAILABLE' }
              }
            }
          }
        }
      })

      if (!product) {
        return NextResponse.json(
          { error: `商品不存在: ${item.productId}` },
          { status: 404 }
        )
      }

      if (product.status !== 'ACTIVE') {
        return NextResponse.json(
          { error: `商品已下架: ${product.name}` },
          { status: 400 }
        )
      }

      if (product._count.cards < item.quantity) {
        return NextResponse.json(
          { error: `库存不足: ${product.name}` },
          { status: 400 }
        )
      }

      const itemTotal = product.price * item.quantity
      totalAmount += itemTotal

      orderItems.push({
        productId: product.id,
        quantity: item.quantity,
        price: product.price,
      })
    }

    // 创建订单
    const order = await prisma.order.create({
      data: {
        email,
        totalAmount,
        status: 'PENDING',
        orderItems: {
          create: orderItems
        }
      },
      include: {
        orderItems: {
          include: {
            product: true
          }
        }
      }
    })

    // 获取商品信息用于 Stripe
    const productDetails = await Promise.all(
      orderItems.map(async (item) => {
        const product = await prisma.product.findUnique({
          where: { id: item.productId }
        })
        return { ...item, productName: product?.name || '商品' }
      })
    )

    // 创建 Stripe Checkout Session
    const session = await stripe.checkout.sessions.create({
      payment_method_types: ['card'],
      line_items: productDetails.map(item => ({
        price_data: {
          currency: 'usd',
          product_data: {
            name: item.productName,
          },
          unit_amount: Math.round(item.price * 100),
        },
        quantity: item.quantity,
      })),
      mode: 'payment',
      success_url: `${process.env.NEXTAUTH_URL}/success?session_id={CHECKOUT_SESSION_ID}`,
      cancel_url: `${process.env.NEXTAUTH_URL}/`,
      customer_email: email,
      metadata: {
        orderId: order.id,
      },
    })

    // 更新订单的 Stripe 支付 ID
    await prisma.order.update({
      where: { id: order.id },
      data: { stripePaymentId: session.id }
    })

    return NextResponse.json({
      sessionId: session.id,
      orderId: order.id,
    })
  } catch (error) {
    console.error('创建支付意图错误:', error)
    return NextResponse.json(
      { error: '服务器错误' },
      { status: 500 }
    )
  }
}
