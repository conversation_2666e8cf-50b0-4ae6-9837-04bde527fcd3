import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'

// 更新商品（仅管理员）
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ productId: string }> }
) {
  try {
    const session = await getServerSession(authOptions)

    if (!session || session.user.role !== 'ADMIN') {
      return NextResponse.json(
        { error: '权限不足' },
        { status: 403 }
      )
    }

    const { productId } = await params
    const { name, description, price, categoryId, image, status } = await request.json()

    if (!name || !price || !categoryId) {
      return NextResponse.json(
        { error: '缺少必需字段' },
        { status: 400 }
      )
    }

    const product = await prisma.product.update({
      where: { id: productId },
      data: {
        name,
        description,
        price: parseFloat(price),
        categoryId,
        image,
        status,
      },
      include: {
        category: true,
        _count: {
          select: {
            cards: {
              where: {
                status: 'AVAILABLE'
              }
            }
          }
        }
      }
    })

    return NextResponse.json(product)
  } catch (error) {
    console.error('更新商品错误:', error)
    return NextResponse.json(
      { error: '服务器错误' },
      { status: 500 }
    )
  }
}

// 删除商品（仅管理员）
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ productId: string }> }
) {
  try {
    const session = await getServerSession(authOptions)

    if (!session || session.user.role !== 'ADMIN') {
      return NextResponse.json(
        { error: '权限不足' },
        { status: 403 }
      )
    }

    const { productId } = await params

    // 检查商品是否存在
    const product = await prisma.product.findUnique({
      where: { id: productId },
      include: {
        _count: {
          select: {
            cards: true,
            orderItems: true
          }
        }
      }
    })

    if (!product) {
      return NextResponse.json(
        { error: '商品不存在' },
        { status: 404 }
      )
    }

    // 如果有关联的订单，不允许删除
    if (product._count.orderItems > 0) {
      return NextResponse.json(
        { error: '该商品已有订单，不能删除' },
        { status: 400 }
      )
    }

    // 先删除相关的卡密
    await prisma.card.deleteMany({
      where: { productId }
    })

    // 删除商品
    await prisma.product.delete({
      where: { id: productId }
    })

    return NextResponse.json({ message: '商品删除成功' })
  } catch (error) {
    console.error('删除商品错误:', error)
    return NextResponse.json(
      { error: '服务器错误' },
      { status: 500 }
    )
  }
}
