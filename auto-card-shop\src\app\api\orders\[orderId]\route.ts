import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'

// 更新订单状态（仅管理员）
export async function PATCH(
  request: NextRequest,
  { params }: { params: Promise<{ orderId: string }> }
) {
  try {
    const session = await getServerSession(authOptions)

    if (!session || session.user.role !== 'ADMIN') {
      return NextResponse.json(
        { error: '权限不足' },
        { status: 403 }
      )
    }

    const { orderId } = await params
    const { status } = await request.json()

    if (!status || !['PENDING', 'PAID', 'DELIVERED', 'CANCELLED', 'REFUNDED'].includes(status)) {
      return NextResponse.json(
        { error: '无效的订单状态' },
        { status: 400 }
      )
    }

    const order = await prisma.order.update({
      where: { id: orderId },
      data: { 
        status,
        updatedAt: new Date()
      },
      include: {
        orderItems: {
          include: {
            product: true
          }
        }
      }
    })

    // 如果状态改为已交付，需要分配卡密
    if (status === 'DELIVERED' && order.status !== 'DELIVERED') {
      for (const orderItem of order.orderItems) {
        // 获取可用的卡密
        const availableCards = await prisma.card.findMany({
          where: {
            productId: orderItem.productId,
            status: 'AVAILABLE'
          },
          take: orderItem.quantity,
          orderBy: {
            createdAt: 'asc'
          }
        })

        if (availableCards.length >= orderItem.quantity) {
          // 标记卡密为已售出
          await prisma.card.updateMany({
            where: {
              id: {
                in: availableCards.map(card => card.id)
              }
            },
            data: {
              status: 'SOLD',
              orderId: order.id,
              usedAt: new Date()
            }
          })

          // 更新商品库存
          await prisma.product.update({
            where: { id: orderItem.productId },
            data: {
              stockCount: {
                decrement: orderItem.quantity
              }
            }
          })
        }
      }
    }

    return NextResponse.json(order)
  } catch (error) {
    console.error('更新订单状态错误:', error)
    return NextResponse.json(
      { error: '服务器错误' },
      { status: 500 }
    )
  }
}
