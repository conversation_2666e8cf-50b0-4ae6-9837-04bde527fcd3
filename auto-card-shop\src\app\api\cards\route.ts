import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'

// 批量添加卡密（仅管理员）
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session || session.user.role !== 'ADMIN') {
      return NextResponse.json(
        { error: '权限不足' },
        { status: 403 }
      )
    }

    const { productId, cards } = await request.json()

    if (!productId || !cards || !Array.isArray(cards)) {
      return NextResponse.json(
        { error: '缺少必需字段' },
        { status: 400 }
      )
    }

    // 验证商品是否存在
    const product = await prisma.product.findUnique({
      where: { id: productId }
    })

    if (!product) {
      return NextResponse.json(
        { error: '商品不存在' },
        { status: 404 }
      )
    }

    // 批量创建卡密
    const createdCards = await prisma.card.createMany({
      data: cards.map((cardData: string) => ({
        productId,
        cardData: cardData.trim(),
      }))
    })

    // 更新商品库存
    await prisma.product.update({
      where: { id: productId },
      data: {
        stockCount: {
          increment: createdCards.count
        }
      }
    })

    return NextResponse.json({
      message: `成功添加 ${createdCards.count} 张卡密`,
      count: createdCards.count
    }, { status: 201 })
  } catch (error) {
    console.error('添加卡密错误:', error)
    return NextResponse.json(
      { error: '服务器错误' },
      { status: 500 }
    )
  }
}

// 获取卡密列表（仅管理员）
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session || session.user.role !== 'ADMIN') {
      return NextResponse.json(
        { error: '权限不足' },
        { status: 403 }
      )
    }

    const { searchParams } = new URL(request.url)
    const productId = searchParams.get('productId')
    const status = searchParams.get('status')

    const where: any = {}
    
    if (productId) {
      where.productId = productId
    }
    
    if (status) {
      where.status = status
    }

    const cards = await prisma.card.findMany({
      where,
      include: {
        product: {
          select: {
            name: true
          }
        }
      },
      orderBy: {
        createdAt: 'desc'
      }
    })

    return NextResponse.json(cards)
  } catch (error) {
    console.error('获取卡密错误:', error)
    return NextResponse.json(
      { error: '服务器错误' },
      { status: 500 }
    )
  }
}
