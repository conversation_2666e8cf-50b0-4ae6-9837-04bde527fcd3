{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///F:/zidongfaka/auto-card-shop/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n\nexport function formatPrice(price: number) {\n  return new Intl.NumberFormat('zh-CN', {\n    style: 'currency',\n    currency: 'USD',\n  }).format(price)\n}\n\nexport function formatDate(date: string | Date) {\n  return new Intl.DateTimeFormat('zh-CN', {\n    year: 'numeric',\n    month: '2-digit',\n    day: '2-digit',\n    hour: '2-digit',\n    minute: '2-digit',\n  }).format(new Date(date))\n}\n"], "names": [], "mappings": ";;;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAEO,SAAS,YAAY,KAAa;IACvC,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;QACpC,OAAO;QACP,UAAU;IACZ,GAAG,MAAM,CAAC;AACZ;AAEO,SAAS,WAAW,IAAmB;IAC5C,OAAO,IAAI,KAAK,cAAc,CAAC,SAAS;QACtC,MAAM;QACN,OAAO;QACP,KAAK;QACL,MAAM;QACN,QAAQ;IACV,GAAG,MAAM,CAAC,IAAI,KAAK;AACrB", "debugId": null}}, {"offset": {"line": 40, "column": 0}, "map": {"version": 3, "sources": ["file:///F:/zidongfaka/auto-card-shop/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50\",\n  {\n    variants: {\n      variant: {\n        default: \"bg-primary text-primary-foreground hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-destructive-foreground hover:bg-destructive/90\",\n        outline:\n          \"border border-input bg-background hover:bg-accent hover:text-accent-foreground\",\n        secondary:\n          \"bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n        ghost: \"hover:bg-accent hover:text-accent-foreground\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-10 px-4 py-2\",\n        sm: \"h-9 rounded-md px-3\",\n        lg: \"h-11 rounded-md px-8\",\n        icon: \"h-10 w-10\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nexport interface ButtonProps\n  extends React.ButtonHTMLAttributes<HTMLButtonElement>,\n    VariantProps<typeof buttonVariants> {\n  asChild?: boolean\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant, size, asChild = false, ...props }, ref) => {\n    return (\n      <button\n        className={cn(buttonVariants({ variant, size, className }))}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nButton.displayName = \"Button\"\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACvB,0RACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aACE;YACF,SACE;YACF,WACE;YACF,OAAO;YACP,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AASF,MAAM,uBAAS,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAC5B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,UAAU,KAAK,EAAE,GAAG,OAAO,EAAE;IACxD,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACxD,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;AAEF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 121, "column": 0}, "map": {"version": 3, "sources": ["file:///F:/zidongfaka/auto-card-shop/src/app/test-purchase/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { Button } from '@/components/ui/button'\nimport { formatPrice } from '@/lib/utils'\nimport { ShoppingCart, CheckCircle } from 'lucide-react'\nimport Link from 'next/link'\n\nexport default function TestPurchasePage() {\n  const [email, setEmail] = useState('')\n  const [loading, setLoading] = useState(false)\n  const [result, setResult] = useState<any>(null)\n  const [error, setError] = useState('')\n\n  const handleTestPurchase = async (e: React.FormEvent) => {\n    e.preventDefault()\n    \n    if (!email) {\n      setError('请输入邮箱地址')\n      return\n    }\n\n    setLoading(true)\n    setError('')\n\n    try {\n      // 模拟购买第一个商品\n      const response = await fetch('/api/test-purchase', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({\n          email,\n          productId: 'cmcj0qg1h000qtwm4egouia83', // 使用第一个商品的ID\n          quantity: 1\n        }),\n      })\n\n      const data = await response.json()\n\n      if (response.ok) {\n        setResult(data)\n      } else {\n        setError(data.error || '购买失败')\n      }\n    } catch (error) {\n      setError('购买失败，请重试')\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  if (result) {\n    return (\n      <div className=\"min-h-screen bg-gray-50\">\n        <nav className=\"bg-white shadow-sm border-b\">\n          <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n            <div className=\"flex justify-between items-center h-16\">\n              <div className=\"flex items-center\">\n                <Link href=\"/\" className=\"text-xl font-bold text-gray-900\">\n                  自动发卡网站\n                </Link>\n              </div>\n              <div className=\"text-sm text-gray-600\">\n                测试购买成功\n              </div>\n            </div>\n          </div>\n        </nav>\n\n        <main className=\"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n          <div className=\"bg-white rounded-lg shadow p-6 mb-6\">\n            <div className=\"flex items-center justify-center mb-4\">\n              <CheckCircle className=\"w-16 h-16 text-green-500\" />\n            </div>\n            <div className=\"text-center\">\n              <h1 className=\"text-2xl font-bold text-gray-900 mb-2\">测试购买成功！</h1>\n              <p className=\"text-gray-600\">以下是您的卡密信息</p>\n            </div>\n          </div>\n\n          <div className=\"bg-white rounded-lg shadow p-6 mb-6\">\n            <h2 className=\"text-lg font-semibold text-gray-900 mb-4\">订单信息</h2>\n            <div className=\"bg-gray-50 rounded-lg p-4 space-y-2 text-sm\">\n              <div className=\"flex justify-between\">\n                <span className=\"text-gray-600\">订单号:</span>\n                <span className=\"font-mono\">{result.orderId}</span>\n              </div>\n              <div className=\"flex justify-between\">\n                <span className=\"text-gray-600\">邮箱:</span>\n                <span>{result.email}</span>\n              </div>\n              <div className=\"flex justify-between\">\n                <span className=\"text-gray-600\">总金额:</span>\n                <span className=\"font-bold text-blue-600\">${result.totalAmount.toFixed(2)}</span>\n              </div>\n            </div>\n          </div>\n\n          {result.cards && Object.keys(result.cards).length > 0 && (\n            <div className=\"space-y-6\">\n              {Object.entries(result.cards).map(([productName, cards]: [string, any[]]) => (\n                <div key={productName} className=\"bg-white rounded-lg shadow p-6\">\n                  <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">{productName}</h3>\n                  <div className=\"space-y-3\">\n                    {cards.map((card, index) => (\n                      <div key={card.id} className=\"bg-gray-50 rounded-lg p-3\">\n                        <div className=\"text-sm text-gray-600 mb-1\">卡密 #{index + 1}</div>\n                        <div className=\"font-mono text-lg text-gray-900 break-all\">\n                          {card.cardData}\n                        </div>\n                      </div>\n                    ))}\n                  </div>\n                </div>\n              ))}\n            </div>\n          )}\n\n          <div className=\"flex justify-center space-x-4 mt-8\">\n            <Link href=\"/\">\n              <Button>返回首页</Button>\n            </Link>\n            <Link href=\"/orders\">\n              <Button variant=\"outline\">查看订单</Button>\n            </Link>\n          </div>\n        </main>\n      </div>\n    )\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      <nav className=\"bg-white shadow-sm border-b\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"flex justify-between items-center h-16\">\n            <div className=\"flex items-center\">\n              <Link href=\"/\" className=\"text-xl font-bold text-gray-900\">\n                自动发卡网站\n              </Link>\n            </div>\n            <div className=\"text-sm text-gray-600\">\n              测试购买\n            </div>\n          </div>\n        </div>\n      </nav>\n\n      <main className=\"max-w-2xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n        <div className=\"bg-white rounded-lg shadow p-6\">\n          <h1 className=\"text-2xl font-bold text-gray-900 mb-6\">测试购买功能</h1>\n          \n          <div className=\"bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-6\">\n            <h3 className=\"text-sm font-medium text-yellow-800 mb-2\">测试说明</h3>\n            <ul className=\"text-sm text-yellow-700 space-y-1\">\n              <li>• 这是一个测试页面，用于验证购买和发卡功能</li>\n              <li>• 不会进行真实支付，直接模拟支付成功</li>\n              <li>• 将自动购买第一个商品并发放卡密</li>\n              <li>• 仅用于开发测试，生产环境请删除此页面</li>\n            </ul>\n          </div>\n\n          <form onSubmit={handleTestPurchase} className=\"space-y-4\">\n            <div>\n              <label htmlFor=\"email\" className=\"block text-sm font-medium text-gray-700 mb-1\">\n                邮箱地址 *\n              </label>\n              <input\n                id=\"email\"\n                type=\"email\"\n                required\n                value={email}\n                onChange={(e) => setEmail(e.target.value)}\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500\"\n                placeholder=\"请输入邮箱地址\"\n              />\n            </div>\n\n            {error && (\n              <div className=\"bg-red-50 border border-red-200 text-red-600 px-4 py-3 rounded-md\">\n                {error}\n              </div>\n            )}\n\n            <div className=\"space-y-3\">\n              <Button\n                type=\"submit\"\n                disabled={loading}\n                className=\"w-full\"\n              >\n                <ShoppingCart className=\"w-4 h-4 mr-2\" />\n                {loading ? '处理中...' : '测试购买'}\n              </Button>\n              \n              <Link href=\"/\">\n                <Button variant=\"outline\" className=\"w-full\">\n                  返回首页\n                </Button>\n              </Link>\n            </div>\n          </form>\n        </div>\n      </main>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAEA;AAAA;AACA;AANA;;;;;;AAQe,SAAS;IACtB,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAO;IAC1C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEnC,MAAM,qBAAqB,OAAO;QAChC,EAAE,cAAc;QAEhB,IAAI,CAAC,OAAO;YACV,SAAS;YACT;QACF;QAEA,WAAW;QACX,SAAS;QAET,IAAI;YACF,YAAY;YACZ,MAAM,WAAW,MAAM,MAAM,sBAAsB;gBACjD,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBACnB;oBACA,WAAW;oBACX,UAAU;gBACZ;YACF;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,SAAS,EAAE,EAAE;gBACf,UAAU;YACZ,OAAO;gBACL,SAAS,KAAK,KAAK,IAAI;YACzB;QACF,EAAE,OAAO,OAAO;YACd,SAAS;QACX,SAAU;YACR,WAAW;QACb;IACF;IAEA,IAAI,QAAQ;QACV,qBACE,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAI,WAAU;kDAAkC;;;;;;;;;;;8CAI7D,8OAAC;oCAAI,WAAU;8CAAwB;;;;;;;;;;;;;;;;;;;;;;8BAO7C,8OAAC;oBAAK,WAAU;;sCACd,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,2NAAA,CAAA,cAAW;wCAAC,WAAU;;;;;;;;;;;8CAEzB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAAwC;;;;;;sDACtD,8OAAC;4CAAE,WAAU;sDAAgB;;;;;;;;;;;;;;;;;;sCAIjC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAA2C;;;;;;8CACzD,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAK,WAAU;8DAAgB;;;;;;8DAChC,8OAAC;oDAAK,WAAU;8DAAa,OAAO,OAAO;;;;;;;;;;;;sDAE7C,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAK,WAAU;8DAAgB;;;;;;8DAChC,8OAAC;8DAAM,OAAO,KAAK;;;;;;;;;;;;sDAErB,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAK,WAAU;8DAAgB;;;;;;8DAChC,8OAAC;oDAAK,WAAU;;wDAA0B;wDAAE,OAAO,WAAW,CAAC,OAAO,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;wBAK5E,OAAO,KAAK,IAAI,OAAO,IAAI,CAAC,OAAO,KAAK,EAAE,MAAM,GAAG,mBAClD,8OAAC;4BAAI,WAAU;sCACZ,OAAO,OAAO,CAAC,OAAO,KAAK,EAAE,GAAG,CAAC,CAAC,CAAC,aAAa,MAAuB,iBACtE,8OAAC;oCAAsB,WAAU;;sDAC/B,8OAAC;4CAAG,WAAU;sDAA4C;;;;;;sDAC1D,8OAAC;4CAAI,WAAU;sDACZ,MAAM,GAAG,CAAC,CAAC,MAAM,sBAChB,8OAAC;oDAAkB,WAAU;;sEAC3B,8OAAC;4DAAI,WAAU;;gEAA6B;gEAAK,QAAQ;;;;;;;sEACzD,8OAAC;4DAAI,WAAU;sEACZ,KAAK,QAAQ;;;;;;;mDAHR,KAAK,EAAE;;;;;;;;;;;mCAJb;;;;;;;;;;sCAiBhB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;8CACT,cAAA,8OAAC,kIAAA,CAAA,SAAM;kDAAC;;;;;;;;;;;8CAEV,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;8CACT,cAAA,8OAAC,kIAAA,CAAA,SAAM;wCAAC,SAAQ;kDAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAMtC;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAI,WAAU;8CAAkC;;;;;;;;;;;0CAI7D,8OAAC;gCAAI,WAAU;0CAAwB;;;;;;;;;;;;;;;;;;;;;;0BAO7C,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAwC;;;;;;sCAEtD,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAA2C;;;;;;8CACzD,8OAAC;oCAAG,WAAU;;sDACZ,8OAAC;sDAAG;;;;;;sDACJ,8OAAC;sDAAG;;;;;;sDACJ,8OAAC;sDAAG;;;;;;sDACJ,8OAAC;sDAAG;;;;;;;;;;;;;;;;;;sCAIR,8OAAC;4BAAK,UAAU;4BAAoB,WAAU;;8CAC5C,8OAAC;;sDACC,8OAAC;4CAAM,SAAQ;4CAAQ,WAAU;sDAA+C;;;;;;sDAGhF,8OAAC;4CACC,IAAG;4CACH,MAAK;4CACL,QAAQ;4CACR,OAAO;4CACP,UAAU,CAAC,IAAM,SAAS,EAAE,MAAM,CAAC,KAAK;4CACxC,WAAU;4CACV,aAAY;;;;;;;;;;;;gCAIf,uBACC,8OAAC;oCAAI,WAAU;8CACZ;;;;;;8CAIL,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,kIAAA,CAAA,SAAM;4CACL,MAAK;4CACL,UAAU;4CACV,WAAU;;8DAEV,8OAAC,sNAAA,CAAA,eAAY;oDAAC,WAAU;;;;;;gDACvB,UAAU,WAAW;;;;;;;sDAGxB,8OAAC,4JAAA,CAAA,UAAI;4CAAC,MAAK;sDACT,cAAA,8OAAC,kIAAA,CAAA,SAAM;gDAAC,SAAQ;gDAAU,WAAU;0DAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAU7D", "debugId": null}}]}