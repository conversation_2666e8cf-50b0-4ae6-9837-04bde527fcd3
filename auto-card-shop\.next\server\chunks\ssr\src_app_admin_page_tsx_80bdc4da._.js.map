{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///F:/zidongfaka/auto-card-shop/src/app/admin/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useEffect, useState } from 'react'\nimport { Package, ShoppingCart, CreditCard, Users } from 'lucide-react'\n\ninterface Stats {\n  totalProducts: number\n  totalOrders: number\n  totalCards: number\n  totalUsers: number\n  recentOrders: any[]\n}\n\nexport default function AdminDashboard() {\n  const [stats, setStats] = useState<Stats>({\n    totalProducts: 0,\n    totalOrders: 0,\n    totalCards: 0,\n    totalUsers: 0,\n    recentOrders: []\n  })\n  const [loading, setLoading] = useState(true)\n\n  useEffect(() => {\n    fetchStats()\n  }, [])\n\n  const fetchStats = async () => {\n    try {\n      // 获取实时统计数据\n      const [productsRes, ordersRes, cardsRes, usersRes] = await Promise.all([\n        fetch('/api/products?status=all'),\n        fetch('/api/orders'),\n        fetch('/api/cards'),\n        fetch('/api/admin/users')\n      ])\n\n      const products = await productsRes.json()\n      const orders = await ordersRes.json()\n      const cards = await cardsRes.json()\n      const users = await usersRes.json()\n\n      setStats({\n        totalProducts: products.length || 0,\n        totalOrders: orders.length || 0,\n        totalCards: cards.length || 0,\n        totalUsers: users.length || 0,\n        recentOrders: orders.slice(0, 5) || []\n      })\n    } catch (error) {\n      console.error('获取统计数据失败:', error)\n      // 使用默认数据\n      setStats({\n        totalProducts: 0,\n        totalOrders: 0,\n        totalCards: 0,\n        totalUsers: 0,\n        recentOrders: []\n      })\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const statCards = [\n    {\n      title: '商品总数',\n      value: stats.totalProducts,\n      icon: Package,\n      color: 'bg-blue-500'\n    },\n    {\n      title: '订单总数',\n      value: stats.totalOrders,\n      icon: ShoppingCart,\n      color: 'bg-green-500'\n    },\n    {\n      title: '卡密总数',\n      value: stats.totalCards,\n      icon: CreditCard,\n      color: 'bg-purple-500'\n    },\n    {\n      title: '用户总数',\n      value: stats.totalUsers,\n      icon: Users,\n      color: 'bg-orange-500'\n    }\n  ]\n\n  if (loading) {\n    return (\n      <div className=\"flex items-center justify-center h-64\">\n        <div className=\"text-gray-500\">加载中...</div>\n      </div>\n    )\n  }\n\n  return (\n    <div className=\"space-y-6\">\n      <div>\n        <h1 className=\"text-2xl font-bold text-gray-900\">仪表板</h1>\n        <p className=\"text-gray-600\">欢迎来到管理后台</p>\n      </div>\n\n      {/* 统计卡片 */}\n      <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\">\n        {statCards.map((card, index) => {\n          const Icon = card.icon\n          return (\n            <div key={index} className=\"bg-white rounded-lg shadow p-6\">\n              <div className=\"flex items-center\">\n                <div className={`${card.color} rounded-lg p-3`}>\n                  <Icon className=\"w-6 h-6 text-white\" />\n                </div>\n                <div className=\"ml-4\">\n                  <p className=\"text-sm font-medium text-gray-600\">{card.title}</p>\n                  <p className=\"text-2xl font-bold text-gray-900\">{card.value}</p>\n                </div>\n              </div>\n            </div>\n          )\n        })}\n      </div>\n\n      {/* 快速操作 */}\n      <div className=\"bg-white rounded-lg shadow p-6\">\n        <h2 className=\"text-lg font-semibold text-gray-900 mb-4\">快速操作</h2>\n        <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\n          <a\n            href=\"/admin/products\"\n            className=\"p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors\"\n          >\n            <Package className=\"w-8 h-8 text-blue-500 mb-2\" />\n            <h3 className=\"font-medium text-gray-900\">管理商品</h3>\n            <p className=\"text-sm text-gray-600\">添加、编辑或删除商品</p>\n          </a>\n          \n          <a\n            href=\"/admin/cards\"\n            className=\"p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors\"\n          >\n            <CreditCard className=\"w-8 h-8 text-purple-500 mb-2\" />\n            <h3 className=\"font-medium text-gray-900\">管理卡密</h3>\n            <p className=\"text-sm text-gray-600\">批量添加或查看卡密</p>\n          </a>\n          \n          <a\n            href=\"/admin/orders\"\n            className=\"p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors\"\n          >\n            <ShoppingCart className=\"w-8 h-8 text-green-500 mb-2\" />\n            <h3 className=\"font-medium text-gray-900\">查看订单</h3>\n            <p className=\"text-sm text-gray-600\">管理和处理订单</p>\n          </a>\n        </div>\n      </div>\n\n      {/* 最近订单 */}\n      {stats.recentOrders.length > 0 && (\n        <div className=\"bg-white rounded-lg shadow p-6\">\n          <h2 className=\"text-lg font-semibold text-gray-900 mb-4\">最近订单</h2>\n          <div className=\"space-y-3\">\n            {stats.recentOrders.map((order: any) => (\n              <div key={order.id} className=\"flex items-center justify-between p-3 bg-gray-50 rounded-lg\">\n                <div>\n                  <div className=\"text-sm font-medium text-gray-900\">\n                    订单 #{order.id.slice(0, 8)}...\n                  </div>\n                  <div className=\"text-xs text-gray-500\">{order.email}</div>\n                </div>\n                <div className=\"text-right\">\n                  <div className=\"text-sm font-medium text-blue-600\">\n                    ${order.totalAmount.toFixed(2)}\n                  </div>\n                  <div className=\"text-xs text-gray-500\">\n                    {new Date(order.createdAt).toLocaleDateString('zh-CN')}\n                  </div>\n                </div>\n              </div>\n            ))}\n          </div>\n          <div className=\"mt-4\">\n            <a\n              href=\"/admin/orders\"\n              className=\"text-sm text-blue-600 hover:text-blue-800\"\n            >\n              查看所有订单 →\n            </a>\n          </div>\n        </div>\n      )}\n\n      {/* 系统信息 */}\n      <div className=\"bg-white rounded-lg shadow p-6\">\n        <h2 className=\"text-lg font-semibold text-gray-900 mb-4\">系统信息</h2>\n        <div className=\"space-y-2 text-sm\">\n          <div className=\"flex justify-between\">\n            <span className=\"text-gray-600\">系统版本:</span>\n            <span className=\"text-gray-900\">v1.0.0</span>\n          </div>\n          <div className=\"flex justify-between\">\n            <span className=\"text-gray-600\">数据库:</span>\n            <span className=\"text-gray-900\">SQLite</span>\n          </div>\n          <div className=\"flex justify-between\">\n            <span className=\"text-gray-600\">支付网关:</span>\n            <span className=\"text-gray-900\">Stripe</span>\n          </div>\n          <div className=\"flex justify-between\">\n            <span className=\"text-gray-600\">最后更新:</span>\n            <span className=\"text-gray-900\">{new Date().toLocaleString('zh-CN')}</span>\n          </div>\n        </div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAHA;;;;AAae,SAAS;IACtB,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAS;QACxC,eAAe;QACf,aAAa;QACb,YAAY;QACZ,YAAY;QACZ,cAAc,EAAE;IAClB;IACA,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG,EAAE;IAEL,MAAM,aAAa;QACjB,IAAI;YACF,WAAW;YACX,MAAM,CAAC,aAAa,WAAW,UAAU,SAAS,GAAG,MAAM,QAAQ,GAAG,CAAC;gBACrE,MAAM;gBACN,MAAM;gBACN,MAAM;gBACN,MAAM;aACP;YAED,MAAM,WAAW,MAAM,YAAY,IAAI;YACvC,MAAM,SAAS,MAAM,UAAU,IAAI;YACnC,MAAM,QAAQ,MAAM,SAAS,IAAI;YACjC,MAAM,QAAQ,MAAM,SAAS,IAAI;YAEjC,SAAS;gBACP,eAAe,SAAS,MAAM,IAAI;gBAClC,aAAa,OAAO,MAAM,IAAI;gBAC9B,YAAY,MAAM,MAAM,IAAI;gBAC5B,YAAY,MAAM,MAAM,IAAI;gBAC5B,cAAc,OAAO,KAAK,CAAC,GAAG,MAAM,EAAE;YACxC;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,aAAa;YAC3B,SAAS;YACT,SAAS;gBACP,eAAe;gBACf,aAAa;gBACb,YAAY;gBACZ,YAAY;gBACZ,cAAc,EAAE;YAClB;QACF,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,YAAY;QAChB;YACE,OAAO;YACP,OAAO,MAAM,aAAa;YAC1B,MAAM,wMAAA,CAAA,UAAO;YACb,OAAO;QACT;QACA;YACE,OAAO;YACP,OAAO,MAAM,WAAW;YACxB,MAAM,sNAAA,CAAA,eAAY;YAClB,OAAO;QACT;QACA;YACE,OAAO;YACP,OAAO,MAAM,UAAU;YACvB,MAAM,kNAAA,CAAA,aAAU;YAChB,OAAO;QACT;QACA;YACE,OAAO;YACP,OAAO,MAAM,UAAU;YACvB,MAAM,oMAAA,CAAA,QAAK;YACX,OAAO;QACT;KACD;IAED,IAAI,SAAS;QACX,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;0BAAgB;;;;;;;;;;;IAGrC;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;;kCACC,8OAAC;wBAAG,WAAU;kCAAmC;;;;;;kCACjD,8OAAC;wBAAE,WAAU;kCAAgB;;;;;;;;;;;;0BAI/B,8OAAC;gBAAI,WAAU;0BACZ,UAAU,GAAG,CAAC,CAAC,MAAM;oBACpB,MAAM,OAAO,KAAK,IAAI;oBACtB,qBACE,8OAAC;wBAAgB,WAAU;kCACzB,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAW,GAAG,KAAK,KAAK,CAAC,eAAe,CAAC;8CAC5C,cAAA,8OAAC;wCAAK,WAAU;;;;;;;;;;;8CAElB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAE,WAAU;sDAAqC,KAAK,KAAK;;;;;;sDAC5D,8OAAC;4CAAE,WAAU;sDAAoC,KAAK,KAAK;;;;;;;;;;;;;;;;;;uBAPvD;;;;;gBAYd;;;;;;0BAIF,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAA2C;;;;;;kCACzD,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCACC,MAAK;gCACL,WAAU;;kDAEV,8OAAC,wMAAA,CAAA,UAAO;wCAAC,WAAU;;;;;;kDACnB,8OAAC;wCAAG,WAAU;kDAA4B;;;;;;kDAC1C,8OAAC;wCAAE,WAAU;kDAAwB;;;;;;;;;;;;0CAGvC,8OAAC;gCACC,MAAK;gCACL,WAAU;;kDAEV,8OAAC,kNAAA,CAAA,aAAU;wCAAC,WAAU;;;;;;kDACtB,8OAAC;wCAAG,WAAU;kDAA4B;;;;;;kDAC1C,8OAAC;wCAAE,WAAU;kDAAwB;;;;;;;;;;;;0CAGvC,8OAAC;gCACC,MAAK;gCACL,WAAU;;kDAEV,8OAAC,sNAAA,CAAA,eAAY;wCAAC,WAAU;;;;;;kDACxB,8OAAC;wCAAG,WAAU;kDAA4B;;;;;;kDAC1C,8OAAC;wCAAE,WAAU;kDAAwB;;;;;;;;;;;;;;;;;;;;;;;;YAM1C,MAAM,YAAY,CAAC,MAAM,GAAG,mBAC3B,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAA2C;;;;;;kCACzD,8OAAC;wBAAI,WAAU;kCACZ,MAAM,YAAY,CAAC,GAAG,CAAC,CAAC,sBACvB,8OAAC;gCAAmB,WAAU;;kDAC5B,8OAAC;;0DACC,8OAAC;gDAAI,WAAU;;oDAAoC;oDAC5C,MAAM,EAAE,CAAC,KAAK,CAAC,GAAG;oDAAG;;;;;;;0DAE5B,8OAAC;gDAAI,WAAU;0DAAyB,MAAM,KAAK;;;;;;;;;;;;kDAErD,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;oDAAoC;oDAC/C,MAAM,WAAW,CAAC,OAAO,CAAC;;;;;;;0DAE9B,8OAAC;gDAAI,WAAU;0DACZ,IAAI,KAAK,MAAM,SAAS,EAAE,kBAAkB,CAAC;;;;;;;;;;;;;+BAZ1C,MAAM,EAAE;;;;;;;;;;kCAkBtB,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BACC,MAAK;4BACL,WAAU;sCACX;;;;;;;;;;;;;;;;;0BAQP,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAA2C;;;;;;kCACzD,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAK,WAAU;kDAAgB;;;;;;kDAChC,8OAAC;wCAAK,WAAU;kDAAgB;;;;;;;;;;;;0CAElC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAK,WAAU;kDAAgB;;;;;;kDAChC,8OAAC;wCAAK,WAAU;kDAAgB;;;;;;;;;;;;0CAElC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAK,WAAU;kDAAgB;;;;;;kDAChC,8OAAC;wCAAK,WAAU;kDAAgB;;;;;;;;;;;;0CAElC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAK,WAAU;kDAAgB;;;;;;kDAChC,8OAAC;wCAAK,WAAU;kDAAiB,IAAI,OAAO,cAAc,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMvE", "debugId": null}}]}