// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "sqlite"
  url      = env("DATABASE_URL")
}

// 用户模型
model User {
  id        String   @id @default(cuid())
  email     String   @unique
  username  String   @unique
  password  String
  role      UserRole @default(USER)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // 关联
  orders   Order[]
  accounts Account[]
  sessions Session[]

  @@map("users")
}

// 用户角色枚举
enum UserRole {
  USER
  ADMIN
}

// NextAuth 账户模型
model Account {
  id                String  @id @default(cuid())
  userId            String
  type              String
  provider          String
  providerAccountId String
  refresh_token     String?
  access_token      String?
  expires_at        Int?
  token_type        String?
  scope             String?
  id_token          String?
  session_state     String?

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([provider, providerAccountId])
  @@map("accounts")
}

// NextAuth 会话模型
model Session {
  id           String   @id @default(cuid())
  sessionToken String   @unique
  userId       String
  expires      DateTime
  user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("sessions")
}

// 商品分类
model Category {
  id          String   @id @default(cuid())
  name        String   @unique
  description String?
  slug        String   @unique
  isActive    Boolean  @default(true)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // 关联
  products Product[]

  @@map("categories")
}

// 商品模型
model Product {
  id          String        @id @default(cuid())
  name        String
  description String?
  price       Float
  image       String?
  categoryId  String
  status      ProductStatus @default(ACTIVE)
  stockCount  Int           @default(0)
  createdAt   DateTime      @default(now())
  updatedAt   DateTime      @updatedAt

  // 关联
  category   Category    @relation(fields: [categoryId], references: [id])
  orderItems OrderItem[]
  cards      Card[]

  @@map("products")
}

// 商品状态枚举
enum ProductStatus {
  ACTIVE
  INACTIVE
  OUT_OF_STOCK
}

// 订单模型
model Order {
  id              String      @id @default(cuid())
  userId          String?
  email           String
  status          OrderStatus @default(PENDING)
  totalAmount     Float
  stripePaymentId String?     @unique
  createdAt       DateTime    @default(now())
  updatedAt       DateTime    @updatedAt

  // 关联
  user       User?       @relation(fields: [userId], references: [id])
  orderItems OrderItem[]

  @@map("orders")
}

// 订单状态枚举
enum OrderStatus {
  PENDING
  PAID
  DELIVERED
  CANCELLED
  REFUNDED
}

// 订单项模型
model OrderItem {
  id        String   @id @default(cuid())
  orderId   String
  productId String
  quantity  Int
  price     Float
  createdAt DateTime @default(now())

  // 关联
  order   Order   @relation(fields: [orderId], references: [id], onDelete: Cascade)
  product Product @relation(fields: [productId], references: [id])

  @@map("order_items")
}

// 卡密模型
model Card {
  id        String     @id @default(cuid())
  productId String
  cardData  String // 卡密内容（加密存储）
  status    CardStatus @default(AVAILABLE)
  orderId   String?
  usedAt    DateTime?
  createdAt DateTime   @default(now())
  updatedAt DateTime   @updatedAt

  // 关联
  product Product @relation(fields: [productId], references: [id])

  @@map("cards")
}

// 卡密状态枚举
enum CardStatus {
  AVAILABLE
  SOLD
  RESERVED
}
