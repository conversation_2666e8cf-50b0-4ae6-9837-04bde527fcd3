{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 108, "column": 0}, "map": {"version": 3, "sources": ["file:///F:/zidongfaka/auto-card-shop/src/lib/stripe.ts"], "sourcesContent": ["import Stripe from 'stripe'\n\nexport const stripe = new Stripe(process.env.STRIPE_SECRET_KEY!, {\n  apiVersion: '2024-12-18.acacia',\n  typescript: true,\n})\n\nexport const getStripe = () => {\n  if (typeof window !== 'undefined') {\n    const { loadStripe } = require('@stripe/stripe-js')\n    return loadStripe(process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY!)\n  }\n  return null\n}\n"], "names": [], "mappings": ";;;;AAAA;;AAEO,MAAM,SAAS,IAAI,wJAAA,CAAA,UAAM,CAAC,QAAQ,GAAG,CAAC,iBAAiB,EAAG;IAC/D,YAAY;IACZ,YAAY;AACd;AAEO,MAAM,YAAY;IACvB,uCAAmC;;IAGnC;IACA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 138, "column": 0}, "map": {"version": 3, "sources": ["file:///F:/zidongfaka/auto-card-shop/src/lib/prisma.ts"], "sourcesContent": ["import { PrismaClient } from '@prisma/client'\n\nconst globalForPrisma = globalThis as unknown as {\n  prisma: PrismaClient | undefined\n}\n\nexport const prisma = globalForPrisma.prisma ?? new PrismaClient()\n\nif (process.env.NODE_ENV !== 'production') globalForPrisma.prisma = prisma\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,kBAAkB;AAIjB,MAAM,SAAS,gBAAgB,MAAM,IAAI,IAAI,6HAAA,CAAA,eAAY;AAEhE,wCAA2C,gBAAgB,MAAM,GAAG", "debugId": null}}, {"offset": {"line": 152, "column": 0}, "map": {"version": 3, "sources": ["file:///F:/zidongfaka/auto-card-shop/src/app/api/payment/create-intent/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server'\nimport { stripe } from '@/lib/stripe'\nimport { prisma } from '@/lib/prisma'\n\nexport async function POST(request: NextRequest) {\n  try {\n    const { items, email } = await request.json()\n\n    if (!items || !Array.isArray(items) || items.length === 0) {\n      return NextResponse.json(\n        { error: '购物车不能为空' },\n        { status: 400 }\n      )\n    }\n\n    if (!email) {\n      return NextResponse.json(\n        { error: '邮箱地址是必需的' },\n        { status: 400 }\n      )\n    }\n\n    // 验证商品并计算总价\n    let totalAmount = 0\n    const orderItems = []\n\n    for (const item of items) {\n      const product = await prisma.product.findUnique({\n        where: { id: item.productId },\n        include: {\n          _count: {\n            select: {\n              cards: {\n                where: { status: 'AVAILABLE' }\n              }\n            }\n          }\n        }\n      })\n\n      if (!product) {\n        return NextResponse.json(\n          { error: `商品不存在: ${item.productId}` },\n          { status: 404 }\n        )\n      }\n\n      if (product.status !== 'ACTIVE') {\n        return NextResponse.json(\n          { error: `商品已下架: ${product.name}` },\n          { status: 400 }\n        )\n      }\n\n      if (product._count.cards < item.quantity) {\n        return NextResponse.json(\n          { error: `库存不足: ${product.name}` },\n          { status: 400 }\n        )\n      }\n\n      const itemTotal = product.price * item.quantity\n      totalAmount += itemTotal\n\n      orderItems.push({\n        productId: product.id,\n        quantity: item.quantity,\n        price: product.price,\n      })\n    }\n\n    // 创建订单\n    const order = await prisma.order.create({\n      data: {\n        email,\n        totalAmount,\n        status: 'PENDING',\n        orderItems: {\n          create: orderItems\n        }\n      },\n      include: {\n        orderItems: {\n          include: {\n            product: true\n          }\n        }\n      }\n    })\n\n    // 获取商品信息用于 Stripe\n    const productDetails = await Promise.all(\n      orderItems.map(async (item) => {\n        const product = await prisma.product.findUnique({\n          where: { id: item.productId }\n        })\n        return { ...item, productName: product?.name || '商品' }\n      })\n    )\n\n    // 创建 Stripe Checkout Session\n    const session = await stripe.checkout.sessions.create({\n      payment_method_types: ['card'],\n      line_items: productDetails.map(item => ({\n        price_data: {\n          currency: 'usd',\n          product_data: {\n            name: item.productName,\n          },\n          unit_amount: Math.round(item.price * 100),\n        },\n        quantity: item.quantity,\n      })),\n      mode: 'payment',\n      success_url: `${process.env.NEXTAUTH_URL}/success?session_id={CHECKOUT_SESSION_ID}`,\n      cancel_url: `${process.env.NEXTAUTH_URL}/`,\n      customer_email: email,\n      metadata: {\n        orderId: order.id,\n      },\n    })\n\n    // 更新订单的 Stripe 支付 ID\n    await prisma.order.update({\n      where: { id: order.id },\n      data: { stripePaymentId: session.id }\n    })\n\n    return NextResponse.json({\n      sessionId: session.id,\n      orderId: order.id,\n    })\n  } catch (error) {\n    console.error('创建支付意图错误:', error)\n    return NextResponse.json(\n      { error: '服务器错误' },\n      { status: 500 }\n    )\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;;;;AAEO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,GAAG,MAAM,QAAQ,IAAI;QAE3C,IAAI,CAAC,SAAS,CAAC,MAAM,OAAO,CAAC,UAAU,MAAM,MAAM,KAAK,GAAG;YACzD,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAU,GACnB;gBAAE,QAAQ;YAAI;QAElB;QAEA,IAAI,CAAC,OAAO;YACV,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAW,GACpB;gBAAE,QAAQ;YAAI;QAElB;QAEA,YAAY;QACZ,IAAI,cAAc;QAClB,MAAM,aAAa,EAAE;QAErB,KAAK,MAAM,QAAQ,MAAO;YACxB,MAAM,UAAU,MAAM,sHAAA,CAAA,SAAM,CAAC,OAAO,CAAC,UAAU,CAAC;gBAC9C,OAAO;oBAAE,IAAI,KAAK,SAAS;gBAAC;gBAC5B,SAAS;oBACP,QAAQ;wBACN,QAAQ;4BACN,OAAO;gCACL,OAAO;oCAAE,QAAQ;gCAAY;4BAC/B;wBACF;oBACF;gBACF;YACF;YAEA,IAAI,CAAC,SAAS;gBACZ,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;oBAAE,OAAO,CAAC,OAAO,EAAE,KAAK,SAAS,EAAE;gBAAC,GACpC;oBAAE,QAAQ;gBAAI;YAElB;YAEA,IAAI,QAAQ,MAAM,KAAK,UAAU;gBAC/B,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;oBAAE,OAAO,CAAC,OAAO,EAAE,QAAQ,IAAI,EAAE;gBAAC,GAClC;oBAAE,QAAQ;gBAAI;YAElB;YAEA,IAAI,QAAQ,MAAM,CAAC,KAAK,GAAG,KAAK,QAAQ,EAAE;gBACxC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;oBAAE,OAAO,CAAC,MAAM,EAAE,QAAQ,IAAI,EAAE;gBAAC,GACjC;oBAAE,QAAQ;gBAAI;YAElB;YAEA,MAAM,YAAY,QAAQ,KAAK,GAAG,KAAK,QAAQ;YAC/C,eAAe;YAEf,WAAW,IAAI,CAAC;gBACd,WAAW,QAAQ,EAAE;gBACrB,UAAU,KAAK,QAAQ;gBACvB,OAAO,QAAQ,KAAK;YACtB;QACF;QAEA,OAAO;QACP,MAAM,QAAQ,MAAM,sHAAA,CAAA,SAAM,CAAC,KAAK,CAAC,MAAM,CAAC;YACtC,MAAM;gBACJ;gBACA;gBACA,QAAQ;gBACR,YAAY;oBACV,QAAQ;gBACV;YACF;YACA,SAAS;gBACP,YAAY;oBACV,SAAS;wBACP,SAAS;oBACX;gBACF;YACF;QACF;QAEA,kBAAkB;QAClB,MAAM,iBAAiB,MAAM,QAAQ,GAAG,CACtC,WAAW,GAAG,CAAC,OAAO;YACpB,MAAM,UAAU,MAAM,sHAAA,CAAA,SAAM,CAAC,OAAO,CAAC,UAAU,CAAC;gBAC9C,OAAO;oBAAE,IAAI,KAAK,SAAS;gBAAC;YAC9B;YACA,OAAO;gBAAE,GAAG,IAAI;gBAAE,aAAa,SAAS,QAAQ;YAAK;QACvD;QAGF,6BAA6B;QAC7B,MAAM,UAAU,MAAM,sHAAA,CAAA,SAAM,CAAC,QAAQ,CAAC,QAAQ,CAAC,MAAM,CAAC;YACpD,sBAAsB;gBAAC;aAAO;YAC9B,YAAY,eAAe,GAAG,CAAC,CAAA,OAAQ,CAAC;oBACtC,YAAY;wBACV,UAAU;wBACV,cAAc;4BACZ,MAAM,KAAK,WAAW;wBACxB;wBACA,aAAa,KAAK,KAAK,CAAC,KAAK,KAAK,GAAG;oBACvC;oBACA,UAAU,KAAK,QAAQ;gBACzB,CAAC;YACD,MAAM;YACN,aAAa,GAAG,QAAQ,GAAG,CAAC,YAAY,CAAC,yCAAyC,CAAC;YACnF,YAAY,GAAG,QAAQ,GAAG,CAAC,YAAY,CAAC,CAAC,CAAC;YAC1C,gBAAgB;YAChB,UAAU;gBACR,SAAS,MAAM,EAAE;YACnB;QACF;QAEA,qBAAqB;QACrB,MAAM,sHAAA,CAAA,SAAM,CAAC,KAAK,CAAC,MAAM,CAAC;YACxB,OAAO;gBAAE,IAAI,MAAM,EAAE;YAAC;YACtB,MAAM;gBAAE,iBAAiB,QAAQ,EAAE;YAAC;QACtC;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,WAAW,QAAQ,EAAE;YACrB,SAAS,MAAM,EAAE;QACnB;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,aAAa;QAC3B,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAQ,GACjB;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}