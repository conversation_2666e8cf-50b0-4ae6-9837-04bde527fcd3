import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'

export async function POST(request: NextRequest) {
  try {
    const { email, productId, quantity = 1 } = await request.json()

    if (!email || !productId) {
      return NextResponse.json(
        { error: '邮箱和商品ID是必需的' },
        { status: 400 }
      )
    }

    // 验证商品并计算总价
    const product = await prisma.product.findUnique({
      where: { id: productId },
      include: {
        _count: {
          select: {
            cards: {
              where: { status: 'AVAILABLE' }
            }
          }
        }
      }
    })

    if (!product) {
      return NextResponse.json(
        { error: '商品不存在' },
        { status: 404 }
      )
    }

    if (product.status !== 'ACTIVE') {
      return NextResponse.json(
        { error: '商品已下架' },
        { status: 400 }
      )
    }

    if (product._count.cards < quantity) {
      return NextResponse.json(
        { error: '库存不足' },
        { status: 400 }
      )
    }

    const totalAmount = product.price * quantity

    // 创建订单
    const order = await prisma.order.create({
      data: {
        email,
        totalAmount,
        status: 'PAID', // 直接设为已支付
        stripePaymentId: `test_${Date.now()}`, // 测试支付ID
        orderItems: {
          create: [{
            productId: product.id,
            quantity: quantity,
            price: product.price,
          }]
        }
      },
      include: {
        orderItems: {
          include: {
            product: true
          }
        }
      }
    })

    // 自动发卡逻辑
    const availableCards = await prisma.card.findMany({
      where: {
        productId: product.id,
        status: 'AVAILABLE'
      },
      take: quantity,
      orderBy: {
        createdAt: 'asc'
      }
    })

    if (availableCards.length >= quantity) {
      // 标记卡密为已售出
      await prisma.card.updateMany({
        where: {
          id: {
            in: availableCards.map(card => card.id)
          }
        },
        data: {
          status: 'SOLD',
          orderId: order.id,
          usedAt: new Date()
        }
      })

      // 更新商品库存
      await prisma.product.update({
        where: { id: product.id },
        data: {
          stockCount: {
            decrement: quantity
          }
        }
      })

      // 更新订单状态为已交付
      await prisma.order.update({
        where: { id: order.id },
        data: { status: 'DELIVERED' }
      })
    }

    // 获取该订单的所有卡密
    const cards = await prisma.card.findMany({
      where: {
        orderId: order.id,
        status: 'SOLD'
      },
      include: {
        product: {
          select: {
            name: true
          }
        }
      },
      orderBy: {
        createdAt: 'asc'
      }
    })

    // 按商品分组卡密
    const cardsByProduct = cards.reduce((acc, card) => {
      const productName = card.product.name
      if (!acc[productName]) {
        acc[productName] = []
      }
      acc[productName].push({
        id: card.id,
        cardData: card.cardData,
        usedAt: card.usedAt
      })
      return acc
    }, {} as Record<string, any[]>)

    return NextResponse.json({
      orderId: order.id,
      email: order.email,
      totalAmount: order.totalAmount,
      status: 'DELIVERED',
      createdAt: order.createdAt,
      cards: cardsByProduct,
      message: '测试购买成功！'
    })
  } catch (error) {
    console.error('测试购买错误:', error)
    return NextResponse.json(
      { error: '服务器错误' },
      { status: 500 }
    )
  }
}
