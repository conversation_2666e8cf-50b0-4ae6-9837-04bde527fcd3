{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 68, "column": 0}, "map": {"version": 3, "sources": ["file:///F:/zidongfaka/auto-card-shop/src/lib/prisma.ts"], "sourcesContent": ["import { PrismaClient } from '@prisma/client'\n\nconst globalForPrisma = globalThis as unknown as {\n  prisma: PrismaClient | undefined\n}\n\nexport const prisma = globalForPrisma.prisma ?? new PrismaClient()\n\nif (process.env.NODE_ENV !== 'production') globalForPrisma.prisma = prisma\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,kBAAkB;AAIjB,MAAM,SAAS,gBAAgB,MAAM,IAAI,IAAI,6HAAA,CAAA,eAAY;AAEhE,wCAA2C,gBAAgB,MAAM,GAAG", "debugId": null}}, {"offset": {"line": 82, "column": 0}, "map": {"version": 3, "sources": ["file:///F:/zidongfaka/auto-card-shop/src/app/api/orders/%5BorderId%5D/cards/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server'\nimport { prisma } from '@/lib/prisma'\n\nexport async function GET(\n  request: NextRequest,\n  { params }: { params: Promise<{ orderId: string }> }\n) {\n  try {\n    const { orderId } = await params\n\n    // 验证订单是否存在且已支付\n    const order = await prisma.order.findUnique({\n      where: { id: orderId },\n      include: {\n        orderItems: {\n          include: {\n            product: true\n          }\n        }\n      }\n    })\n\n    if (!order) {\n      return NextResponse.json(\n        { error: '订单不存在' },\n        { status: 404 }\n      )\n    }\n\n    if (order.status !== 'DELIVERED') {\n      return NextResponse.json(\n        { error: '订单尚未完成支付或处理' },\n        { status: 400 }\n      )\n    }\n\n    // 获取该订单的所有卡密\n    const cards = await prisma.card.findMany({\n      where: {\n        orderId: orderId,\n        status: 'SOLD'\n      },\n      include: {\n        product: {\n          select: {\n            name: true\n          }\n        }\n      },\n      orderBy: {\n        createdAt: 'asc'\n      }\n    })\n\n    // 按商品分组卡密\n    const cardsByProduct = cards.reduce((acc, card) => {\n      const productName = card.product.name\n      if (!acc[productName]) {\n        acc[productName] = []\n      }\n      acc[productName].push({\n        id: card.id,\n        cardData: card.cardData,\n        usedAt: card.usedAt\n      })\n      return acc\n    }, {} as Record<string, any[]>)\n\n    return NextResponse.json({\n      orderId: order.id,\n      email: order.email,\n      totalAmount: order.totalAmount,\n      status: order.status,\n      createdAt: order.createdAt,\n      cards: cardsByProduct\n    })\n  } catch (error) {\n    console.error('获取订单卡密错误:', error)\n    return NextResponse.json(\n      { error: '服务器错误' },\n      { status: 500 }\n    )\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,eAAe,IACpB,OAAoB,EACpB,EAAE,MAAM,EAA4C;IAEpD,IAAI;QACF,MAAM,EAAE,OAAO,EAAE,GAAG,MAAM;QAE1B,eAAe;QACf,MAAM,QAAQ,MAAM,sHAAA,CAAA,SAAM,CAAC,KAAK,CAAC,UAAU,CAAC;YAC1C,OAAO;gBAAE,IAAI;YAAQ;YACrB,SAAS;gBACP,YAAY;oBACV,SAAS;wBACP,SAAS;oBACX;gBACF;YACF;QACF;QAEA,IAAI,CAAC,OAAO;YACV,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAQ,GACjB;gBAAE,QAAQ;YAAI;QAElB;QAEA,IAAI,MAAM,MAAM,KAAK,aAAa;YAChC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAc,GACvB;gBAAE,QAAQ;YAAI;QAElB;QAEA,aAAa;QACb,MAAM,QAAQ,MAAM,sHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,QAAQ,CAAC;YACvC,OAAO;gBACL,SAAS;gBACT,QAAQ;YACV;YACA,SAAS;gBACP,SAAS;oBACP,QAAQ;wBACN,MAAM;oBACR;gBACF;YACF;YACA,SAAS;gBACP,WAAW;YACb;QACF;QAEA,UAAU;QACV,MAAM,iBAAiB,MAAM,MAAM,CAAC,CAAC,KAAK;YACxC,MAAM,cAAc,KAAK,OAAO,CAAC,IAAI;YACrC,IAAI,CAAC,GAAG,CAAC,YAAY,EAAE;gBACrB,GAAG,CAAC,YAAY,GAAG,EAAE;YACvB;YACA,GAAG,CAAC,YAAY,CAAC,IAAI,CAAC;gBACpB,IAAI,KAAK,EAAE;gBACX,UAAU,KAAK,QAAQ;gBACvB,QAAQ,KAAK,MAAM;YACrB;YACA,OAAO;QACT,GAAG,CAAC;QAEJ,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS,MAAM,EAAE;YACjB,OAAO,MAAM,KAAK;YAClB,aAAa,MAAM,WAAW;YAC9B,QAAQ,MAAM,MAAM;YACpB,WAAW,MAAM,SAAS;YAC1B,OAAO;QACT;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,aAAa;QAC3B,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAQ,GACjB;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}