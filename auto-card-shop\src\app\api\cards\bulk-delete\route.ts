import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'

// 批量删除卡密（仅管理员）
export async function DELETE(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session || session.user.role !== 'ADMIN') {
      return NextResponse.json(
        { error: '权限不足' },
        { status: 403 }
      )
    }

    const { status, productId } = await request.json()

    if (!status || !['AVAILABLE', 'SOLD', 'RESERVED'].includes(status)) {
      return NextResponse.json(
        { error: '无效的卡密状态' },
        { status: 400 }
      )
    }

    const where: any = { status }
    if (productId) {
      where.productId = productId
    }

    // 获取要删除的卡密数量
    const cardsToDelete = await prisma.card.findMany({
      where,
      select: {
        id: true,
        productId: true
      }
    })

    if (cardsToDelete.length === 0) {
      return NextResponse.json(
        { error: '没有找到符合条件的卡密' },
        { status: 404 }
      )
    }

    // 按商品分组统计
    const productCounts = cardsToDelete.reduce((acc, card) => {
      acc[card.productId] = (acc[card.productId] || 0) + 1
      return acc
    }, {} as Record<string, number>)

    // 删除卡密
    const deleteResult = await prisma.card.deleteMany({
      where
    })

    // 如果删除的是可用卡密，需要更新商品库存
    if (status === 'AVAILABLE') {
      for (const [productId, count] of Object.entries(productCounts)) {
        await prisma.product.update({
          where: { id: productId },
          data: {
            stockCount: {
              decrement: count
            }
          }
        })
      }
    }

    return NextResponse.json({
      message: `成功删除 ${deleteResult.count} 张卡密`,
      count: deleteResult.count
    })
  } catch (error) {
    console.error('批量删除卡密错误:', error)
    return NextResponse.json(
      { error: '服务器错误' },
      { status: 500 }
    )
  }
}
