# Stripe 沙箱测试指南

## 🎯 测试环境配置

### 1. Stripe 测试密钥
项目已配置 Stripe 测试环境密钥：
- **公钥**: `pk_test_51RX2b807MUSF0mxRWMbKrsy7ZM9xlhzAkDKS8AJoEf09StuCHhSJFOM7tpgEOca0CzTChJG1AaPXr9aycjU1WV6200puW6u6z0`
- **私钥**: `sk_test_51RX2b807MUSF0mxRWMbKrsy7ZM9xlhzAkDKS8AJoEf09StuCHhSJFOM7tpgEOca0CzTChJG1AaPXr9aycjU1WV6200puW6u6z0`

### 2. 测试页面
- **Stripe 测试页面**: http://localhost:3000/stripe-test
- **免支付测试**: http://localhost:3000/test-purchase

## 💳 Stripe 测试卡号

### 成功支付测试卡
| 卡号 | 描述 | 结果 |
|------|------|------|
| `****************` | Visa 成功支付 | ✅ 支付成功 |
| `****************` | Visa 借记卡 | ✅ 支付成功 |
| `****************` | Mastercard | ✅ 支付成功 |
| `2223003122003222` | Mastercard (2-series) | ✅ 支付成功 |
| `****************` | Visa (3D Secure) | ✅ 支付成功 |

### 失败支付测试卡
| 卡号 | 描述 | 结果 |
|------|------|------|
| `****************` | 卡片被拒绝 | ❌ 支付失败 |
| `****************` | 资金不足 | ❌ 支付失败 |
| `****************` | 卡片过期 | ❌ 支付失败 |
| `****************` | 卡片过期 | ❌ 支付失败 |
| `****************` | CVC 错误 | ❌ 支付失败 |

### 测试卡信息
- **过期日期**: 任何未来日期（如 `12/34`）
- **CVC**: 任何3位数字（如 `123`）
- **邮政编码**: 任何5位数字（如 `12345`）
- **持卡人姓名**: 任何名称

## 🧪 测试流程

### 方法一：使用 Stripe 测试页面
1. 访问 http://localhost:3000/stripe-test
2. 输入测试邮箱（默认：<EMAIL>）
3. 点击"测试 Stripe 支付"
4. 系统会跳转到 Stripe Checkout 页面
5. 使用测试卡号完成支付
6. 支付成功后返回成功页面并查看卡密

### 方法二：通过正常购买流程
1. 访问首页 http://localhost:3000
2. 选择任意商品点击"立即购买"
3. 在结算页面输入邮箱
4. 点击支付按钮
5. 在 Stripe 页面使用测试卡号
6. 完成支付流程

### 方法三：免支付测试
1. 访问 http://localhost:3000/test-purchase
2. 输入邮箱地址
3. 点击"测试购买"
4. 直接模拟支付成功并查看卡密

## 🔄 支付流程说明

### 1. 创建支付会话
```javascript
// 前端调用
const response = await fetch('/api/payment/create-intent', {
  method: 'POST',
  body: JSON.stringify({
    items: [{ productId, quantity }],
    email
  })
})
```

### 2. Stripe Checkout
```javascript
// 跳转到 Stripe 支付页面
const stripe = await loadStripe(STRIPE_PUBLIC_KEY)
await stripe.redirectToCheckout({ sessionId })
```

### 3. 支付成功处理
- Stripe 会调用 webhook: `/api/webhooks/stripe`
- 系统自动发放卡密
- 更新订单状态为"已交付"
- 用户跳转到成功页面

## 📋 测试检查清单

### ✅ 基础功能测试
- [ ] 商品页面正常显示
- [ ] 购买按钮正常工作
- [ ] 结算页面信息正确
- [ ] Stripe 支付页面正常跳转

### ✅ 支付测试
- [ ] 成功支付卡号测试
- [ ] 失败支付卡号测试
- [ ] 支付成功后自动发卡
- [ ] 订单状态正确更新

### ✅ 卡密发放测试
- [ ] 支付成功后立即发放卡密
- [ ] 卡密数据正确显示
- [ ] 库存正确扣减
- [ ] 订单记录完整

### ✅ 错误处理测试
- [ ] 库存不足时的处理
- [ ] 支付失败时的处理
- [ ] 网络错误时的处理
- [ ] 无效商品时的处理

## 🚨 注意事项

### 开发环境
- ✅ 使用测试密钥，不会产生真实费用
- ✅ 所有支付都是模拟的
- ✅ 可以使用测试卡号进行各种场景测试

### 生产环境准备
- 🔄 替换为生产环境 Stripe 密钥
- 🔄 配置真实的 Webhook 端点
- 🔄 删除测试页面
- 🔄 设置正确的成功/取消 URL

## 🔗 相关链接

- [Stripe 测试卡号文档](https://stripe.com/docs/testing)
- [Stripe Checkout 文档](https://stripe.com/docs/checkout)
- [Stripe Webhooks 文档](https://stripe.com/docs/webhooks)

## 🐛 常见问题

### Q: 支付页面无法加载？
A: 检查 `NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY` 环境变量是否正确设置

### Q: 支付成功但没有发卡？
A: 检查 Webhook 配置和 `STRIPE_WEBHOOK_SECRET` 设置

### Q: 测试卡号被拒绝？
A: 确保使用正确的测试卡号格式，并检查过期日期和 CVC

### Q: 订单状态没有更新？
A: 检查数据库连接和 Webhook 处理逻辑

---

🎉 **测试成功标志**: 能够完成完整的购买流程并自动获得卡密！
