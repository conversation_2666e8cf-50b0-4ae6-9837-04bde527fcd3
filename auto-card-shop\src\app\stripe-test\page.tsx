'use client'

import { useState } from 'react'
import { Button } from '@/components/ui/button'
import { formatPrice } from '@/lib/utils'
import { loadStripe } from '@stripe/stripe-js'
import { CreditCard, TestTube, AlertCircle, CheckCircle } from 'lucide-react'
import Link from 'next/link'

export default function StripeTestPage() {
  const [email, setEmail] = useState('<EMAIL>')
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState('')
  const [success, setSuccess] = useState('')

  const testCards = [
    {
      number: '****************',
      description: '成功支付',
      type: 'success'
    },
    {
      number: '****************',
      description: '卡片被拒绝',
      type: 'decline'
    },
    {
      number: '****************',
      description: '资金不足',
      type: 'decline'
    },
    {
      number: '****************',
      description: '卡片过期',
      type: 'decline'
    },
    {
      number: '****************',
      description: '卡片过期',
      type: 'decline'
    }
  ]

  const handleStripeTest = async () => {
    setLoading(true)
    setError('')
    setSuccess('')

    try {
      // 创建支付会话
      const response = await fetch('/api/payment/create-intent', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          items: [{
            productId: 'cmcj0qg1h000qtwm4egouia83', // 使用第一个商品
            quantity: 1
          }],
          email: email
        }),
      })

      const data = await response.json()

      if (!response.ok) {
        throw new Error(data.error || '创建支付会话失败')
      }

      // 初始化 Stripe
      const stripe = await loadStripe(process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY!)
      
      if (!stripe) {
        throw new Error('Stripe 初始化失败')
      }

      setSuccess('支付会话创建成功！正在跳转到 Stripe 支付页面...')

      // 跳转到 Stripe Checkout
      const { error: stripeError } = await stripe.redirectToCheckout({
        sessionId: data.sessionId
      })

      if (stripeError) {
        throw new Error(stripeError.message)
      }

    } catch (error: any) {
      setError(error.message || '测试失败，请重试')
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* 导航栏 */}
      <nav className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <Link href="/" className="text-xl font-bold text-gray-900">
                自动发卡网站
              </Link>
            </div>
            <div className="text-sm text-gray-600">
              Stripe 沙箱测试
            </div>
          </div>
        </div>
      </nav>

      <main className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="bg-white rounded-lg shadow p-6 mb-6">
          <div className="flex items-center mb-4">
            <TestTube className="w-6 h-6 text-blue-600 mr-2" />
            <h1 className="text-2xl font-bold text-gray-900">Stripe 沙箱测试</h1>
          </div>
          <p className="text-gray-600">
            测试 Stripe 支付集成，使用测试卡号进行沙箱环境支付测试
          </p>
        </div>

        {/* 测试卡号信息 */}
        <div className="bg-white rounded-lg shadow p-6 mb-6">
          <h2 className="text-lg font-semibold text-gray-900 mb-4">Stripe 测试卡号</h2>
          <div className="space-y-3">
            {testCards.map((card, index) => (
              <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <div>
                  <div className="font-mono text-sm text-gray-900">{card.number}</div>
                  <div className="text-xs text-gray-600">{card.description}</div>
                </div>
                <div className={`px-2 py-1 rounded text-xs font-medium ${
                  card.type === 'success' 
                    ? 'bg-green-100 text-green-800' 
                    : 'bg-red-100 text-red-800'
                }`}>
                  {card.type === 'success' ? '成功' : '失败'}
                </div>
              </div>
            ))}
          </div>
          
          <div className="mt-4 p-4 bg-blue-50 border border-blue-200 rounded-lg">
            <h3 className="text-sm font-medium text-blue-900 mb-2">测试信息</h3>
            <ul className="text-sm text-blue-800 space-y-1">
              <li>• <strong>过期日期</strong>: 任何未来日期（如 12/34）</li>
              <li>• <strong>CVC</strong>: 任何3位数字（如 123）</li>
              <li>• <strong>邮政编码</strong>: 任何5位数字（如 12345）</li>
              <li>• <strong>持卡人姓名</strong>: 任何名称</li>
            </ul>
          </div>
        </div>

        {/* 测试表单 */}
        <div className="bg-white rounded-lg shadow p-6">
          <h2 className="text-lg font-semibold text-gray-900 mb-4">开始测试</h2>
          
          <div className="space-y-4">
            <div>
              <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-1">
                测试邮箱
              </label>
              <input
                id="email"
                type="email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                placeholder="<EMAIL>"
              />
            </div>

            <div className="bg-gray-50 rounded-lg p-4">
              <h3 className="text-sm font-medium text-gray-900 mb-2">测试商品</h3>
              <div className="flex justify-between items-center">
                <span className="text-gray-700">Office 365 个人版</span>
                <span className="font-bold text-blue-600">{formatPrice(69.99)}</span>
              </div>
            </div>

            {error && (
              <div className="flex items-center p-4 bg-red-50 border border-red-200 rounded-md">
                <AlertCircle className="w-5 h-5 text-red-600 mr-2" />
                <span className="text-red-600">{error}</span>
              </div>
            )}

            {success && (
              <div className="flex items-center p-4 bg-green-50 border border-green-200 rounded-md">
                <CheckCircle className="w-5 h-5 text-green-600 mr-2" />
                <span className="text-green-600">{success}</span>
              </div>
            )}

            <div className="space-y-3">
              <Button
                onClick={handleStripeTest}
                disabled={loading}
                className="w-full"
                size="lg"
              >
                <CreditCard className="w-5 h-5 mr-2" />
                {loading ? '创建支付会话中...' : '测试 Stripe 支付'}
              </Button>
              
              <div className="text-center">
                <Link href="/test-purchase" className="text-blue-600 hover:text-blue-800 text-sm">
                  或使用免支付测试
                </Link>
              </div>
            </div>
          </div>
        </div>

        {/* 测试流程说明 */}
        <div className="bg-white rounded-lg shadow p-6 mt-6">
          <h2 className="text-lg font-semibold text-gray-900 mb-4">测试流程</h2>
          <div className="space-y-3">
            <div className="flex items-start">
              <div className="w-6 h-6 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center text-sm font-medium mr-3 mt-0.5">
                1
              </div>
              <div>
                <div className="font-medium text-gray-900">点击测试按钮</div>
                <div className="text-sm text-gray-600">系统将创建 Stripe 支付会话</div>
              </div>
            </div>
            
            <div className="flex items-start">
              <div className="w-6 h-6 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center text-sm font-medium mr-3 mt-0.5">
                2
              </div>
              <div>
                <div className="font-medium text-gray-900">跳转到 Stripe 支付页面</div>
                <div className="text-sm text-gray-600">使用上面的测试卡号进行支付</div>
              </div>
            </div>
            
            <div className="flex items-start">
              <div className="w-6 h-6 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center text-sm font-medium mr-3 mt-0.5">
                3
              </div>
              <div>
                <div className="font-medium text-gray-900">支付成功后返回</div>
                <div className="text-sm text-gray-600">查看卡密自动发放结果</div>
              </div>
            </div>
          </div>
        </div>

        {/* 返回按钮 */}
        <div className="flex justify-center mt-8">
          <Link href="/">
            <Button variant="outline">
              返回首页
            </Button>
          </Link>
        </div>
      </main>
    </div>
  )
}
