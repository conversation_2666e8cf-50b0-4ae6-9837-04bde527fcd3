import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ orderId: string }> }
) {
  try {
    const { orderId } = await params

    // 验证订单是否存在且已支付
    const order = await prisma.order.findUnique({
      where: { id: orderId },
      include: {
        orderItems: {
          include: {
            product: true
          }
        }
      }
    })

    if (!order) {
      return NextResponse.json(
        { error: '订单不存在' },
        { status: 404 }
      )
    }

    if (order.status !== 'DELIVERED') {
      return NextResponse.json(
        { error: '订单尚未完成支付或处理' },
        { status: 400 }
      )
    }

    // 获取该订单的所有卡密
    const cards = await prisma.card.findMany({
      where: {
        orderId: orderId,
        status: 'SOLD'
      },
      include: {
        product: {
          select: {
            name: true
          }
        }
      },
      orderBy: {
        createdAt: 'asc'
      }
    })

    // 按商品分组卡密
    const cardsByProduct = cards.reduce((acc, card) => {
      const productName = card.product.name
      if (!acc[productName]) {
        acc[productName] = []
      }
      acc[productName].push({
        id: card.id,
        cardData: card.cardData,
        usedAt: card.usedAt
      })
      return acc
    }, {} as Record<string, any[]>)

    return NextResponse.json({
      orderId: order.id,
      email: order.email,
      totalAmount: order.totalAmount,
      status: order.status,
      createdAt: order.createdAt,
      cards: cardsByProduct
    })
  } catch (error) {
    console.error('获取订单卡密错误:', error)
    return NextResponse.json(
      { error: '服务器错误' },
      { status: 500 }
    )
  }
}
