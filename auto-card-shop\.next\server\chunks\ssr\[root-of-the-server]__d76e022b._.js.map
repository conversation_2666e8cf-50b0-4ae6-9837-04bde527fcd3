{"version": 3, "sources": [], "sections": [{"offset": {"line": 31, "column": 0}, "map": {"version": 3, "sources": ["file:///F:/zidongfaka/auto-card-shop/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n\nexport function formatPrice(price: number) {\n  return new Intl.NumberFormat('zh-CN', {\n    style: 'currency',\n    currency: 'USD',\n  }).format(price)\n}\n\nexport function formatDate(date: string | Date) {\n  return new Intl.DateTimeFormat('zh-CN', {\n    year: 'numeric',\n    month: '2-digit',\n    day: '2-digit',\n    hour: '2-digit',\n    minute: '2-digit',\n  }).format(new Date(date))\n}\n"], "names": [], "mappings": ";;;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAEO,SAAS,YAAY,KAAa;IACvC,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;QACpC,OAAO;QACP,UAAU;IACZ,GAAG,MAAM,CAAC;AACZ;AAEO,SAAS,WAAW,IAAmB;IAC5C,OAAO,IAAI,KAAK,cAAc,CAAC,SAAS;QACtC,MAAM;QACN,OAAO;QACP,KAAK;QACL,MAAM;QACN,QAAQ;IACV,GAAG,MAAM,CAAC,IAAI,KAAK;AACrB", "debugId": null}}, {"offset": {"line": 64, "column": 0}, "map": {"version": 3, "sources": ["file:///F:/zidongfaka/auto-card-shop/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50\",\n  {\n    variants: {\n      variant: {\n        default: \"bg-primary text-primary-foreground hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-destructive-foreground hover:bg-destructive/90\",\n        outline:\n          \"border border-input bg-background hover:bg-accent hover:text-accent-foreground\",\n        secondary:\n          \"bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n        ghost: \"hover:bg-accent hover:text-accent-foreground\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-10 px-4 py-2\",\n        sm: \"h-9 rounded-md px-3\",\n        lg: \"h-11 rounded-md px-8\",\n        icon: \"h-10 w-10\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nexport interface ButtonProps\n  extends React.ButtonHTMLAttributes<HTMLButtonElement>,\n    VariantProps<typeof buttonVariants> {\n  asChild?: boolean\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant, size, asChild = false, ...props }, ref) => {\n    return (\n      <button\n        className={cn(buttonVariants({ variant, size, className }))}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nButton.displayName = \"Button\"\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACvB,0RACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aACE;YACF,SACE;YACF,WACE;YACF,OAAO;YACP,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AASF,MAAM,uBAAS,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAC5B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,UAAU,KAAK,EAAE,GAAG,OAAO,EAAE;IACxD,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACxD,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;AAEF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 121, "column": 0}, "map": {"version": 3, "sources": ["file:///F:/zidongfaka/auto-card-shop/src/app/orders/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { useSession } from 'next-auth/react'\nimport { useRouter } from 'next/navigation'\nimport { Button } from '@/components/ui/button'\nimport { Search, Eye } from 'lucide-react'\n\nexport default function OrdersPage() {\n  const { data: session } = useSession()\n  const router = useRouter()\n  const [email, setEmail] = useState('')\n  const [orderId, setOrderId] = useState('')\n  const [orders, setOrders] = useState([])\n  const [loading, setLoading] = useState(false)\n  const [error, setError] = useState('')\n\n  const handleSearch = async (e: React.FormEvent) => {\n    e.preventDefault()\n    \n    if (!email && !orderId) {\n      setError('请输入邮箱地址或订单号')\n      return\n    }\n\n    setLoading(true)\n    setError('')\n\n    try {\n      const params = new URLSearchParams()\n      if (email) params.append('email', email)\n      if (orderId) params.append('orderId', orderId)\n\n      const response = await fetch(`/api/orders?${params}`)\n      const data = await response.json()\n\n      if (response.ok) {\n        setOrders(data)\n        if (data.length === 0) {\n          setError('未找到相关订单')\n        }\n      } else {\n        setError(data.error || '查询失败')\n      }\n    } catch (error) {\n      setError('查询失败，请重试')\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const viewOrderCards = (orderId: string) => {\n    router.push(`/orders/${orderId}/cards`)\n  }\n\n  const getStatusColor = (status: string) => {\n    switch (status) {\n      case 'PENDING':\n        return 'bg-yellow-100 text-yellow-800'\n      case 'PAID':\n        return 'bg-blue-100 text-blue-800'\n      case 'DELIVERED':\n        return 'bg-green-100 text-green-800'\n      case 'CANCELLED':\n        return 'bg-red-100 text-red-800'\n      case 'REFUNDED':\n        return 'bg-gray-100 text-gray-800'\n      default:\n        return 'bg-gray-100 text-gray-800'\n    }\n  }\n\n  const getStatusText = (status: string) => {\n    switch (status) {\n      case 'PENDING':\n        return '待支付'\n      case 'PAID':\n        return '已支付'\n      case 'DELIVERED':\n        return '已交付'\n      case 'CANCELLED':\n        return '已取消'\n      case 'REFUNDED':\n        return '已退款'\n      default:\n        return status\n    }\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      {/* 导航栏 */}\n      <nav className=\"bg-white shadow-sm border-b\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"flex justify-between items-center h-16\">\n            <div className=\"flex items-center\">\n              <a href=\"/\" className=\"text-xl font-bold text-gray-900\">\n                自动发卡网站\n              </a>\n            </div>\n            <div className=\"text-sm text-gray-600\">\n              订单查询\n            </div>\n          </div>\n        </div>\n      </nav>\n\n      <main className=\"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n        <div className=\"bg-white rounded-lg shadow p-6\">\n          <h1 className=\"text-2xl font-bold text-gray-900 mb-6\">订单查询</h1>\n          \n          {/* 搜索表单 */}\n          <form onSubmit={handleSearch} className=\"mb-8\">\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4 mb-4\">\n              <div>\n                <label htmlFor=\"email\" className=\"block text-sm font-medium text-gray-700 mb-1\">\n                  邮箱地址\n                </label>\n                <input\n                  id=\"email\"\n                  type=\"email\"\n                  value={email}\n                  onChange={(e) => setEmail(e.target.value)}\n                  className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500\"\n                  placeholder=\"请输入购买时使用的邮箱\"\n                />\n              </div>\n              \n              <div>\n                <label htmlFor=\"orderId\" className=\"block text-sm font-medium text-gray-700 mb-1\">\n                  订单号（可选）\n                </label>\n                <input\n                  id=\"orderId\"\n                  type=\"text\"\n                  value={orderId}\n                  onChange={(e) => setOrderId(e.target.value)}\n                  className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500\"\n                  placeholder=\"请输入订单号\"\n                />\n              </div>\n            </div>\n            \n            {error && (\n              <div className=\"mb-4 bg-red-50 border border-red-200 text-red-600 px-4 py-3 rounded-md\">\n                {error}\n              </div>\n            )}\n            \n            <Button type=\"submit\" disabled={loading}>\n              <Search className=\"w-4 h-4 mr-2\" />\n              {loading ? '查询中...' : '查询订单'}\n            </Button>\n          </form>\n\n          {/* 订单列表 */}\n          {orders.length > 0 && (\n            <div className=\"space-y-4\">\n              <h2 className=\"text-lg font-semibold text-gray-900\">查询结果</h2>\n              {orders.map((order: any) => (\n                <div key={order.id} className=\"border border-gray-200 rounded-lg p-4\">\n                  <div className=\"flex items-center justify-between mb-3\">\n                    <div>\n                      <div className=\"text-sm text-gray-500\">订单号: {order.id}</div>\n                      <div className=\"text-sm text-gray-500\">\n                        创建时间: {new Date(order.createdAt).toLocaleString('zh-CN')}\n                      </div>\n                    </div>\n                    <div className=\"flex items-center space-x-3\">\n                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(order.status)}`}>\n                        {getStatusText(order.status)}\n                      </span>\n                      <div className=\"text-lg font-bold text-blue-600\">\n                        ${order.totalAmount.toFixed(2)}\n                      </div>\n                    </div>\n                  </div>\n                  \n                  <div className=\"space-y-2 mb-3\">\n                    {order.orderItems.map((item: any) => (\n                      <div key={item.id} className=\"flex justify-between text-sm\">\n                        <span>{item.product.name}</span>\n                        <span>{item.quantity} x ${item.price.toFixed(2)}</span>\n                      </div>\n                    ))}\n                  </div>\n                  \n                  {order.status === 'DELIVERED' && (\n                    <Button\n                      onClick={() => viewOrderCards(order.id)}\n                      variant=\"outline\"\n                      size=\"sm\"\n                    >\n                      <Eye className=\"w-4 h-4 mr-2\" />\n                      查看卡密\n                    </Button>\n                  )}\n                </div>\n              ))}\n            </div>\n          )}\n        </div>\n      </main>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAAA;AANA;;;;;;;AAQe,SAAS;IACtB,MAAM,EAAE,MAAM,OAAO,EAAE,GAAG,CAAA,GAAA,8IAAA,CAAA,aAAU,AAAD;IACnC,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,EAAE;IACvC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEnC,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAEhB,IAAI,CAAC,SAAS,CAAC,SAAS;YACtB,SAAS;YACT;QACF;QAEA,WAAW;QACX,SAAS;QAET,IAAI;YACF,MAAM,SAAS,IAAI;YACnB,IAAI,OAAO,OAAO,MAAM,CAAC,SAAS;YAClC,IAAI,SAAS,OAAO,MAAM,CAAC,WAAW;YAEtC,MAAM,WAAW,MAAM,MAAM,CAAC,YAAY,EAAE,QAAQ;YACpD,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,SAAS,EAAE,EAAE;gBACf,UAAU;gBACV,IAAI,KAAK,MAAM,KAAK,GAAG;oBACrB,SAAS;gBACX;YACF,OAAO;gBACL,SAAS,KAAK,KAAK,IAAI;YACzB;QACF,EAAE,OAAO,OAAO;YACd,SAAS;QACX,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,OAAO,IAAI,CAAC,CAAC,QAAQ,EAAE,QAAQ,MAAM,CAAC;IACxC;IAEA,MAAM,iBAAiB,CAAC;QACtB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,gBAAgB,CAAC;QACrB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAE,MAAK;oCAAI,WAAU;8CAAkC;;;;;;;;;;;0CAI1D,8OAAC;gCAAI,WAAU;0CAAwB;;;;;;;;;;;;;;;;;;;;;;0BAO7C,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAwC;;;;;;sCAGtD,8OAAC;4BAAK,UAAU;4BAAc,WAAU;;8CACtC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;;8DACC,8OAAC;oDAAM,SAAQ;oDAAQ,WAAU;8DAA+C;;;;;;8DAGhF,8OAAC;oDACC,IAAG;oDACH,MAAK;oDACL,OAAO;oDACP,UAAU,CAAC,IAAM,SAAS,EAAE,MAAM,CAAC,KAAK;oDACxC,WAAU;oDACV,aAAY;;;;;;;;;;;;sDAIhB,8OAAC;;8DACC,8OAAC;oDAAM,SAAQ;oDAAU,WAAU;8DAA+C;;;;;;8DAGlF,8OAAC;oDACC,IAAG;oDACH,MAAK;oDACL,OAAO;oDACP,UAAU,CAAC,IAAM,WAAW,EAAE,MAAM,CAAC,KAAK;oDAC1C,WAAU;oDACV,aAAY;;;;;;;;;;;;;;;;;;gCAKjB,uBACC,8OAAC;oCAAI,WAAU;8CACZ;;;;;;8CAIL,8OAAC,kIAAA,CAAA,SAAM;oCAAC,MAAK;oCAAS,UAAU;;sDAC9B,8OAAC,sMAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;wCACjB,UAAU,WAAW;;;;;;;;;;;;;wBAKzB,OAAO,MAAM,GAAG,mBACf,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAsC;;;;;;gCACnD,OAAO,GAAG,CAAC,CAAC,sBACX,8OAAC;wCAAmB,WAAU;;0DAC5B,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;;0EACC,8OAAC;gEAAI,WAAU;;oEAAwB;oEAAM,MAAM,EAAE;;;;;;;0EACrD,8OAAC;gEAAI,WAAU;;oEAAwB;oEAC9B,IAAI,KAAK,MAAM,SAAS,EAAE,cAAc,CAAC;;;;;;;;;;;;;kEAGpD,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAK,WAAW,CAAC,2CAA2C,EAAE,eAAe,MAAM,MAAM,GAAG;0EAC1F,cAAc,MAAM,MAAM;;;;;;0EAE7B,8OAAC;gEAAI,WAAU;;oEAAkC;oEAC7C,MAAM,WAAW,CAAC,OAAO,CAAC;;;;;;;;;;;;;;;;;;;0DAKlC,8OAAC;gDAAI,WAAU;0DACZ,MAAM,UAAU,CAAC,GAAG,CAAC,CAAC,qBACrB,8OAAC;wDAAkB,WAAU;;0EAC3B,8OAAC;0EAAM,KAAK,OAAO,CAAC,IAAI;;;;;;0EACxB,8OAAC;;oEAAM,KAAK,QAAQ;oEAAC;oEAAK,KAAK,KAAK,CAAC,OAAO,CAAC;;;;;;;;uDAFrC,KAAK,EAAE;;;;;;;;;;4CAOpB,MAAM,MAAM,KAAK,6BAChB,8OAAC,kIAAA,CAAA,SAAM;gDACL,SAAS,IAAM,eAAe,MAAM,EAAE;gDACtC,SAAQ;gDACR,MAAK;;kEAEL,8OAAC,gMAAA,CAAA,MAAG;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;;uCAjC5B,MAAM,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA6ClC", "debugId": null}}]}