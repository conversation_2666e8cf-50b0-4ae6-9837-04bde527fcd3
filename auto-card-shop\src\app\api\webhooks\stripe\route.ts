import { NextRequest, NextResponse } from 'next/server'
import { stripe } from '@/lib/stripe'
import { prisma } from '@/lib/prisma'
import { headers } from 'next/headers'

export async function POST(request: NextRequest) {
  try {
    const body = await request.text()
    const signature = headers().get('stripe-signature')

    if (!signature) {
      return NextResponse.json(
        { error: '缺少 Stripe 签名' },
        { status: 400 }
      )
    }

    let event
    try {
      event = stripe.webhooks.constructEvent(
        body,
        signature,
        process.env.STRIPE_WEBHOOK_SECRET!
      )
    } catch (err) {
      console.error('Webhook 签名验证失败:', err)
      return NextResponse.json(
        { error: 'Webhook 签名验证失败' },
        { status: 400 }
      )
    }

    // 处理支付成功事件
    if (event.type === 'checkout.session.completed') {
      const session = event.data.object
      const orderId = session.metadata?.orderId

      if (!orderId) {
        console.error('订单 ID 缺失')
        return NextResponse.json({ error: '订单 ID 缺失' }, { status: 400 })
      }

      // 更新订单状态为已支付
      const order = await prisma.order.update({
        where: { id: orderId },
        data: { status: 'PAID' },
        include: {
          orderItems: {
            include: {
              product: true
            }
          }
        }
      })

      // 自动发卡逻辑
      for (const orderItem of order.orderItems) {
        // 获取可用的卡密
        const availableCards = await prisma.card.findMany({
          where: {
            productId: orderItem.productId,
            status: 'AVAILABLE'
          },
          take: orderItem.quantity,
          orderBy: {
            createdAt: 'asc'
          }
        })

        if (availableCards.length < orderItem.quantity) {
          console.error(`库存不足: ${orderItem.product.name}`)
          continue
        }

        // 标记卡密为已售出
        await prisma.card.updateMany({
          where: {
            id: {
              in: availableCards.map(card => card.id)
            }
          },
          data: {
            status: 'SOLD',
            orderId: order.id,
            usedAt: new Date()
          }
        })

        // 更新商品库存
        await prisma.product.update({
          where: { id: orderItem.productId },
          data: {
            stockCount: {
              decrement: orderItem.quantity
            }
          }
        })
      }

      // 更新订单状态为已交付
      await prisma.order.update({
        where: { id: orderId },
        data: { status: 'DELIVERED' }
      })

      console.log(`订单 ${orderId} 处理完成`)
    }

    return NextResponse.json({ received: true })
  } catch (error) {
    console.error('Webhook 处理错误:', error)
    return NextResponse.json(
      { error: '服务器错误' },
      { status: 500 }
    )
  }
}
