'use client'

import { useEffect, useState } from 'react'
import { useSearchParams } from 'next/navigation'
import { Button } from '@/components/ui/button'
import { CheckCircle, Copy, Download, Home } from 'lucide-react'
import Link from 'next/link'

interface OrderData {
  orderId: string
  email: string
  totalAmount: number
  status: string
  createdAt: string
  cards: Record<string, Array<{
    id: string
    cardData: string
    usedAt: string
  }>>
}

export default function SuccessPage() {
  const searchParams = useSearchParams()
  const sessionId = searchParams.get('session_id')
  
  const [orderData, setOrderData] = useState<OrderData | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState('')
  const [copiedCard, setCopiedCard] = useState('')

  useEffect(() => {
    if (sessionId) {
      fetchOrderData()
    } else {
      setError('缺少支付会话信息')
      setLoading(false)
    }
  }, [sessionId])

  const fetchOrderData = async () => {
    try {
      const response = await fetch(`/api/payment/success?session_id=${sessionId}`)
      const data = await response.json()

      if (response.ok) {
        setOrderData(data)
      } else {
        setError(data.error || '获取订单信息失败')
      }
    } catch (error) {
      setError('获取订单信息失败')
    } finally {
      setLoading(false)
    }
  }

  const copyToClipboard = async (text: string, cardId: string) => {
    try {
      await navigator.clipboard.writeText(text)
      setCopiedCard(cardId)
      setTimeout(() => setCopiedCard(''), 2000)
    } catch (error) {
      console.error('复制失败:', error)
    }
  }

  const downloadCards = () => {
    if (!orderData) return

    let content = `订单号: ${orderData.orderId}\n`
    content += `邮箱: ${orderData.email}\n`
    content += `总金额: $${orderData.totalAmount.toFixed(2)}\n`
    content += `购买时间: ${new Date(orderData.createdAt).toLocaleString('zh-CN')}\n\n`

    Object.entries(orderData.cards).forEach(([productName, cards]) => {
      content += `=== ${productName} ===\n`
      cards.forEach((card, index) => {
        content += `${index + 1}. ${card.cardData}\n`
      })
      content += '\n'
    })

    const blob = new Blob([content], { type: 'text/plain;charset=utf-8' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `order-${orderData.orderId}-cards.txt`
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-gray-500">处理中...</div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="text-red-600 mb-4">{error}</div>
          <Link href="/">
            <Button variant="outline">
              <Home className="w-4 h-4 mr-2" />
              返回首页
            </Button>
          </Link>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* 导航栏 */}
      <nav className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <Link href="/" className="text-xl font-bold text-gray-900">
                自动发卡网站
              </Link>
            </div>
            <div className="text-sm text-gray-600">
              支付成功
            </div>
          </div>
        </div>
      </nav>

      <main className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* 成功提示 */}
        <div className="bg-white rounded-lg shadow p-6 mb-6">
          <div className="flex items-center justify-center mb-4">
            <CheckCircle className="w-16 h-16 text-green-500" />
          </div>
          <div className="text-center">
            <h1 className="text-2xl font-bold text-gray-900 mb-2">支付成功！</h1>
            <p className="text-gray-600">您的订单已完成，卡密已自动发放</p>
          </div>
        </div>

        {orderData && (
          <>
            {/* 订单信息 */}
            <div className="bg-white rounded-lg shadow p-6 mb-6">
              <div className="flex justify-between items-center mb-4">
                <h2 className="text-lg font-semibold text-gray-900">订单信息</h2>
                <Button onClick={downloadCards} variant="outline">
                  <Download className="w-4 h-4 mr-2" />
                  下载卡密
                </Button>
              </div>
              
              <div className="bg-gray-50 rounded-lg p-4 space-y-2 text-sm">
                <div className="flex justify-between">
                  <span className="text-gray-600">订单号:</span>
                  <span className="font-mono">{orderData.orderId}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">邮箱:</span>
                  <span>{orderData.email}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">总金额:</span>
                  <span className="font-bold text-blue-600">${orderData.totalAmount.toFixed(2)}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">购买时间:</span>
                  <span>{new Date(orderData.createdAt).toLocaleString('zh-CN')}</span>
                </div>
              </div>
            </div>

            {/* 卡密信息 */}
            <div className="space-y-6">
              {Object.entries(orderData.cards).map(([productName, cards]) => (
                <div key={productName} className="bg-white rounded-lg shadow p-6">
                  <h3 className="text-lg font-semibold text-gray-900 mb-4">{productName}</h3>
                  <div className="space-y-3">
                    {cards.map((card, index) => (
                      <div key={card.id} className="flex items-center justify-between bg-gray-50 rounded-lg p-3">
                        <div className="flex-1">
                          <div className="text-sm text-gray-600 mb-1">卡密 #{index + 1}</div>
                          <div className="font-mono text-lg text-gray-900 break-all">
                            {card.cardData}
                          </div>
                        </div>
                        <Button
                          onClick={() => copyToClipboard(card.cardData, card.id)}
                          variant="outline"
                          size="sm"
                          className="ml-3"
                        >
                          <Copy className="w-4 h-4 mr-1" />
                          {copiedCard === card.id ? '已复制' : '复制'}
                        </Button>
                      </div>
                    ))}
                  </div>
                </div>
              ))}
            </div>

            {/* 使用说明 */}
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-6 mt-6">
              <h3 className="text-sm font-medium text-blue-900 mb-2">重要提示</h3>
              <ul className="text-sm text-blue-800 space-y-1">
                <li>• 请妥善保管您的卡密信息，避免泄露给他人</li>
                <li>• 卡密一旦使用后无法退换，请确认后再使用</li>
                <li>• 建议立即下载保存卡密信息以备后用</li>
                <li>• 如有任何问题，请联系客服</li>
                <li>• 您也可以通过订单查询页面重新查看卡密</li>
              </ul>
            </div>

            {/* 操作按钮 */}
            <div className="flex justify-center space-x-4 mt-8">
              <Link href="/">
                <Button>
                  <Home className="w-4 h-4 mr-2" />
                  返回首页
                </Button>
              </Link>
              <Link href="/orders">
                <Button variant="outline">
                  查看我的订单
                </Button>
              </Link>
            </div>
          </>
        )}
      </main>
    </div>
  )
}
