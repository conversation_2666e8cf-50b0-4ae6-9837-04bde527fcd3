{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 68, "column": 0}, "map": {"version": 3, "sources": ["file:///F:/zidongfaka/auto-card-shop/src/lib/prisma.ts"], "sourcesContent": ["import { PrismaClient } from '@prisma/client'\n\nconst globalForPrisma = globalThis as unknown as {\n  prisma: PrismaClient | undefined\n}\n\nexport const prisma = globalForPrisma.prisma ?? new PrismaClient()\n\nif (process.env.NODE_ENV !== 'production') globalForPrisma.prisma = prisma\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,kBAAkB;AAIjB,MAAM,SAAS,gBAAgB,MAAM,IAAI,IAAI,6HAAA,CAAA,eAAY;AAEhE,wCAA2C,gBAAgB,MAAM,GAAG", "debugId": null}}, {"offset": {"line": 82, "column": 0}, "map": {"version": 3, "sources": ["file:///F:/zidongfaka/auto-card-shop/src/app/api/test-purchase/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server'\nimport { prisma } from '@/lib/prisma'\n\nexport async function POST(request: NextRequest) {\n  try {\n    const { email, productId, quantity = 1 } = await request.json()\n\n    if (!email || !productId) {\n      return NextResponse.json(\n        { error: '邮箱和商品ID是必需的' },\n        { status: 400 }\n      )\n    }\n\n    // 验证商品并计算总价\n    const product = await prisma.product.findUnique({\n      where: { id: productId },\n      include: {\n        _count: {\n          select: {\n            cards: {\n              where: { status: 'AVAILABLE' }\n            }\n          }\n        }\n      }\n    })\n\n    if (!product) {\n      return NextResponse.json(\n        { error: '商品不存在' },\n        { status: 404 }\n      )\n    }\n\n    if (product.status !== 'ACTIVE') {\n      return NextResponse.json(\n        { error: '商品已下架' },\n        { status: 400 }\n      )\n    }\n\n    if (product._count.cards < quantity) {\n      return NextResponse.json(\n        { error: '库存不足' },\n        { status: 400 }\n      )\n    }\n\n    const totalAmount = product.price * quantity\n\n    // 创建订单\n    const order = await prisma.order.create({\n      data: {\n        email,\n        totalAmount,\n        status: 'PAID', // 直接设为已支付\n        stripePaymentId: `test_${Date.now()}`, // 测试支付ID\n        orderItems: {\n          create: [{\n            productId: product.id,\n            quantity: quantity,\n            price: product.price,\n          }]\n        }\n      },\n      include: {\n        orderItems: {\n          include: {\n            product: true\n          }\n        }\n      }\n    })\n\n    // 自动发卡逻辑\n    const availableCards = await prisma.card.findMany({\n      where: {\n        productId: product.id,\n        status: 'AVAILABLE'\n      },\n      take: quantity,\n      orderBy: {\n        createdAt: 'asc'\n      }\n    })\n\n    if (availableCards.length >= quantity) {\n      // 标记卡密为已售出\n      await prisma.card.updateMany({\n        where: {\n          id: {\n            in: availableCards.map(card => card.id)\n          }\n        },\n        data: {\n          status: 'SOLD',\n          orderId: order.id,\n          usedAt: new Date()\n        }\n      })\n\n      // 更新商品库存\n      await prisma.product.update({\n        where: { id: product.id },\n        data: {\n          stockCount: {\n            decrement: quantity\n          }\n        }\n      })\n\n      // 更新订单状态为已交付\n      await prisma.order.update({\n        where: { id: order.id },\n        data: { status: 'DELIVERED' }\n      })\n    }\n\n    // 获取该订单的所有卡密\n    const cards = await prisma.card.findMany({\n      where: {\n        orderId: order.id,\n        status: 'SOLD'\n      },\n      include: {\n        product: {\n          select: {\n            name: true\n          }\n        }\n      },\n      orderBy: {\n        createdAt: 'asc'\n      }\n    })\n\n    // 按商品分组卡密\n    const cardsByProduct = cards.reduce((acc, card) => {\n      const productName = card.product.name\n      if (!acc[productName]) {\n        acc[productName] = []\n      }\n      acc[productName].push({\n        id: card.id,\n        cardData: card.cardData,\n        usedAt: card.usedAt\n      })\n      return acc\n    }, {} as Record<string, any[]>)\n\n    return NextResponse.json({\n      orderId: order.id,\n      email: order.email,\n      totalAmount: order.totalAmount,\n      status: 'DELIVERED',\n      createdAt: order.createdAt,\n      cards: cardsByProduct,\n      message: '测试购买成功！'\n    })\n  } catch (error) {\n    console.error('测试购买错误:', error)\n    return NextResponse.json(\n      { error: '服务器错误' },\n      { status: 500 }\n    )\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,MAAM,EAAE,KAAK,EAAE,SAAS,EAAE,WAAW,CAAC,EAAE,GAAG,MAAM,QAAQ,IAAI;QAE7D,IAAI,CAAC,SAAS,CAAC,WAAW;YACxB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAc,GACvB;gBAAE,QAAQ;YAAI;QAElB;QAEA,YAAY;QACZ,MAAM,UAAU,MAAM,sHAAA,CAAA,SAAM,CAAC,OAAO,CAAC,UAAU,CAAC;YAC9C,OAAO;gBAAE,IAAI;YAAU;YACvB,SAAS;gBACP,QAAQ;oBACN,QAAQ;wBACN,OAAO;4BACL,OAAO;gCAAE,QAAQ;4BAAY;wBAC/B;oBACF;gBACF;YACF;QACF;QAEA,IAAI,CAAC,SAAS;YACZ,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAQ,GACjB;gBAAE,QAAQ;YAAI;QAElB;QAEA,IAAI,QAAQ,MAAM,KAAK,UAAU;YAC/B,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAQ,GACjB;gBAAE,QAAQ;YAAI;QAElB;QAEA,IAAI,QAAQ,MAAM,CAAC,KAAK,GAAG,UAAU;YACnC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAO,GAChB;gBAAE,QAAQ;YAAI;QAElB;QAEA,MAAM,cAAc,QAAQ,KAAK,GAAG;QAEpC,OAAO;QACP,MAAM,QAAQ,MAAM,sHAAA,CAAA,SAAM,CAAC,KAAK,CAAC,MAAM,CAAC;YACtC,MAAM;gBACJ;gBACA;gBACA,QAAQ;gBACR,iBAAiB,CAAC,KAAK,EAAE,KAAK,GAAG,IAAI;gBACrC,YAAY;oBACV,QAAQ;wBAAC;4BACP,WAAW,QAAQ,EAAE;4BACrB,UAAU;4BACV,OAAO,QAAQ,KAAK;wBACtB;qBAAE;gBACJ;YACF;YACA,SAAS;gBACP,YAAY;oBACV,SAAS;wBACP,SAAS;oBACX;gBACF;YACF;QACF;QAEA,SAAS;QACT,MAAM,iBAAiB,MAAM,sHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,QAAQ,CAAC;YAChD,OAAO;gBACL,WAAW,QAAQ,EAAE;gBACrB,QAAQ;YACV;YACA,MAAM;YACN,SAAS;gBACP,WAAW;YACb;QACF;QAEA,IAAI,eAAe,MAAM,IAAI,UAAU;YACrC,WAAW;YACX,MAAM,sHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,UAAU,CAAC;gBAC3B,OAAO;oBACL,IAAI;wBACF,IAAI,eAAe,GAAG,CAAC,CAAA,OAAQ,KAAK,EAAE;oBACxC;gBACF;gBACA,MAAM;oBACJ,QAAQ;oBACR,SAAS,MAAM,EAAE;oBACjB,QAAQ,IAAI;gBACd;YACF;YAEA,SAAS;YACT,MAAM,sHAAA,CAAA,SAAM,CAAC,OAAO,CAAC,MAAM,CAAC;gBAC1B,OAAO;oBAAE,IAAI,QAAQ,EAAE;gBAAC;gBACxB,MAAM;oBACJ,YAAY;wBACV,WAAW;oBACb;gBACF;YACF;YAEA,aAAa;YACb,MAAM,sHAAA,CAAA,SAAM,CAAC,KAAK,CAAC,MAAM,CAAC;gBACxB,OAAO;oBAAE,IAAI,MAAM,EAAE;gBAAC;gBACtB,MAAM;oBAAE,QAAQ;gBAAY;YAC9B;QACF;QAEA,aAAa;QACb,MAAM,QAAQ,MAAM,sHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,QAAQ,CAAC;YACvC,OAAO;gBACL,SAAS,MAAM,EAAE;gBACjB,QAAQ;YACV;YACA,SAAS;gBACP,SAAS;oBACP,QAAQ;wBACN,MAAM;oBACR;gBACF;YACF;YACA,SAAS;gBACP,WAAW;YACb;QACF;QAEA,UAAU;QACV,MAAM,iBAAiB,MAAM,MAAM,CAAC,CAAC,KAAK;YACxC,MAAM,cAAc,KAAK,OAAO,CAAC,IAAI;YACrC,IAAI,CAAC,GAAG,CAAC,YAAY,EAAE;gBACrB,GAAG,CAAC,YAAY,GAAG,EAAE;YACvB;YACA,GAAG,CAAC,YAAY,CAAC,IAAI,CAAC;gBACpB,IAAI,KAAK,EAAE;gBACX,UAAU,KAAK,QAAQ;gBACvB,QAAQ,KAAK,MAAM;YACrB;YACA,OAAO;QACT,GAAG,CAAC;QAEJ,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS,MAAM,EAAE;YACjB,OAAO,MAAM,KAAK;YAClB,aAAa,MAAM,WAAW;YAC9B,QAAQ;YACR,WAAW,MAAM,SAAS;YAC1B,OAAO;YACP,SAAS;QACX;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,WAAW;QACzB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAQ,GACjB;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}