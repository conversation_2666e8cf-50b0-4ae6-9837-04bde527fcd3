{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///F:/zidongfaka/auto-card-shop/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n\nexport function formatPrice(price: number) {\n  return new Intl.NumberFormat('zh-CN', {\n    style: 'currency',\n    currency: 'USD',\n  }).format(price)\n}\n\nexport function formatDate(date: string | Date) {\n  return new Intl.DateTimeFormat('zh-CN', {\n    year: 'numeric',\n    month: '2-digit',\n    day: '2-digit',\n    hour: '2-digit',\n    minute: '2-digit',\n  }).format(new Date(date))\n}\n"], "names": [], "mappings": ";;;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAEO,SAAS,YAAY,KAAa;IACvC,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;QACpC,OAAO;QACP,UAAU;IACZ,GAAG,MAAM,CAAC;AACZ;AAEO,SAAS,WAAW,IAAmB;IAC5C,OAAO,IAAI,KAAK,cAAc,CAAC,SAAS;QACtC,MAAM;QACN,OAAO;QACP,KAAK;QACL,MAAM;QACN,QAAQ;IACV,GAAG,MAAM,CAAC,IAAI,KAAK;AACrB", "debugId": null}}, {"offset": {"line": 43, "column": 0}, "map": {"version": 3, "sources": ["file:///F:/zidongfaka/auto-card-shop/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50\",\n  {\n    variants: {\n      variant: {\n        default: \"bg-primary text-primary-foreground hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-destructive-foreground hover:bg-destructive/90\",\n        outline:\n          \"border border-input bg-background hover:bg-accent hover:text-accent-foreground\",\n        secondary:\n          \"bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n        ghost: \"hover:bg-accent hover:text-accent-foreground\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-10 px-4 py-2\",\n        sm: \"h-9 rounded-md px-3\",\n        lg: \"h-11 rounded-md px-8\",\n        icon: \"h-10 w-10\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nexport interface ButtonProps\n  extends React.ButtonHTMLAttributes<HTMLButtonElement>,\n    VariantProps<typeof buttonVariants> {\n  asChild?: boolean\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant, size, asChild = false, ...props }, ref) => {\n    return (\n      <button\n        className={cn(buttonVariants({ variant, size, className }))}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nButton.displayName = \"Button\"\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACvB,0RACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aACE;YACF,SACE;YACF,WACE;YACF,OAAO;YACP,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AASF,MAAM,uBAAS,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAC5B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,UAAU,KAAK,EAAE,GAAG,OAAO,EAAE;IACxD,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACxD,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;;AAEF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 107, "column": 0}, "map": {"version": 3, "sources": ["file:///F:/zidongfaka/auto-card-shop/src/app/stripe-test/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { Button } from '@/components/ui/button'\nimport { formatPrice } from '@/lib/utils'\nimport { loadStripe } from '@stripe/stripe-js'\nimport { CreditCard, TestTube, AlertCircle, CheckCircle } from 'lucide-react'\nimport Link from 'next/link'\n\nexport default function StripeTestPage() {\n  const [email, setEmail] = useState('<EMAIL>')\n  const [loading, setLoading] = useState(false)\n  const [error, setError] = useState('')\n  const [success, setSuccess] = useState('')\n\n  const testCards = [\n    {\n      number: '****************',\n      description: '成功支付',\n      type: 'success'\n    },\n    {\n      number: '****************',\n      description: '卡片被拒绝',\n      type: 'decline'\n    },\n    {\n      number: '****************',\n      description: '资金不足',\n      type: 'decline'\n    },\n    {\n      number: '****************',\n      description: '卡片过期',\n      type: 'decline'\n    },\n    {\n      number: '****************',\n      description: '卡片过期',\n      type: 'decline'\n    }\n  ]\n\n  const handleStripeTest = async () => {\n    setLoading(true)\n    setError('')\n    setSuccess('')\n\n    try {\n      // 创建支付会话\n      const response = await fetch('/api/payment/create-intent', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({\n          items: [{\n            productId: 'cmcj0qg1h000qtwm4egouia83', // 使用第一个商品\n            quantity: 1\n          }],\n          email: email\n        }),\n      })\n\n      const data = await response.json()\n\n      if (!response.ok) {\n        throw new Error(data.error || '创建支付会话失败')\n      }\n\n      // 初始化 Stripe\n      const stripe = await loadStripe(process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY!)\n      \n      if (!stripe) {\n        throw new Error('Stripe 初始化失败')\n      }\n\n      setSuccess('支付会话创建成功！正在跳转到 Stripe 支付页面...')\n\n      // 跳转到 Stripe Checkout\n      const { error: stripeError } = await stripe.redirectToCheckout({\n        sessionId: data.sessionId\n      })\n\n      if (stripeError) {\n        throw new Error(stripeError.message)\n      }\n\n    } catch (error: any) {\n      setError(error.message || '测试失败，请重试')\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      {/* 导航栏 */}\n      <nav className=\"bg-white shadow-sm border-b\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"flex justify-between items-center h-16\">\n            <div className=\"flex items-center\">\n              <Link href=\"/\" className=\"text-xl font-bold text-gray-900\">\n                自动发卡网站\n              </Link>\n            </div>\n            <div className=\"text-sm text-gray-600\">\n              Stripe 沙箱测试\n            </div>\n          </div>\n        </div>\n      </nav>\n\n      <main className=\"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n        <div className=\"bg-white rounded-lg shadow p-6 mb-6\">\n          <div className=\"flex items-center mb-4\">\n            <TestTube className=\"w-6 h-6 text-blue-600 mr-2\" />\n            <h1 className=\"text-2xl font-bold text-gray-900\">Stripe 沙箱测试</h1>\n          </div>\n          <p className=\"text-gray-600\">\n            测试 Stripe 支付集成，使用测试卡号进行沙箱环境支付测试\n          </p>\n        </div>\n\n        {/* 测试卡号信息 */}\n        <div className=\"bg-white rounded-lg shadow p-6 mb-6\">\n          <h2 className=\"text-lg font-semibold text-gray-900 mb-4\">Stripe 测试卡号</h2>\n          <div className=\"space-y-3\">\n            {testCards.map((card, index) => (\n              <div key={index} className=\"flex items-center justify-between p-3 bg-gray-50 rounded-lg\">\n                <div>\n                  <div className=\"font-mono text-sm text-gray-900\">{card.number}</div>\n                  <div className=\"text-xs text-gray-600\">{card.description}</div>\n                </div>\n                <div className={`px-2 py-1 rounded text-xs font-medium ${\n                  card.type === 'success' \n                    ? 'bg-green-100 text-green-800' \n                    : 'bg-red-100 text-red-800'\n                }`}>\n                  {card.type === 'success' ? '成功' : '失败'}\n                </div>\n              </div>\n            ))}\n          </div>\n          \n          <div className=\"mt-4 p-4 bg-blue-50 border border-blue-200 rounded-lg\">\n            <h3 className=\"text-sm font-medium text-blue-900 mb-2\">测试信息</h3>\n            <ul className=\"text-sm text-blue-800 space-y-1\">\n              <li>• <strong>过期日期</strong>: 任何未来日期（如 12/34）</li>\n              <li>• <strong>CVC</strong>: 任何3位数字（如 123）</li>\n              <li>• <strong>邮政编码</strong>: 任何5位数字（如 12345）</li>\n              <li>• <strong>持卡人姓名</strong>: 任何名称</li>\n            </ul>\n          </div>\n        </div>\n\n        {/* 测试表单 */}\n        <div className=\"bg-white rounded-lg shadow p-6\">\n          <h2 className=\"text-lg font-semibold text-gray-900 mb-4\">开始测试</h2>\n          \n          <div className=\"space-y-4\">\n            <div>\n              <label htmlFor=\"email\" className=\"block text-sm font-medium text-gray-700 mb-1\">\n                测试邮箱\n              </label>\n              <input\n                id=\"email\"\n                type=\"email\"\n                value={email}\n                onChange={(e) => setEmail(e.target.value)}\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500\"\n                placeholder=\"<EMAIL>\"\n              />\n            </div>\n\n            <div className=\"bg-gray-50 rounded-lg p-4\">\n              <h3 className=\"text-sm font-medium text-gray-900 mb-2\">测试商品</h3>\n              <div className=\"flex justify-between items-center\">\n                <span className=\"text-gray-700\">Office 365 个人版</span>\n                <span className=\"font-bold text-blue-600\">{formatPrice(69.99)}</span>\n              </div>\n            </div>\n\n            {error && (\n              <div className=\"flex items-center p-4 bg-red-50 border border-red-200 rounded-md\">\n                <AlertCircle className=\"w-5 h-5 text-red-600 mr-2\" />\n                <span className=\"text-red-600\">{error}</span>\n              </div>\n            )}\n\n            {success && (\n              <div className=\"flex items-center p-4 bg-green-50 border border-green-200 rounded-md\">\n                <CheckCircle className=\"w-5 h-5 text-green-600 mr-2\" />\n                <span className=\"text-green-600\">{success}</span>\n              </div>\n            )}\n\n            <div className=\"space-y-3\">\n              <Button\n                onClick={handleStripeTest}\n                disabled={loading}\n                className=\"w-full\"\n                size=\"lg\"\n              >\n                <CreditCard className=\"w-5 h-5 mr-2\" />\n                {loading ? '创建支付会话中...' : '测试 Stripe 支付'}\n              </Button>\n              \n              <div className=\"text-center\">\n                <Link href=\"/test-purchase\" className=\"text-blue-600 hover:text-blue-800 text-sm\">\n                  或使用免支付测试\n                </Link>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* 测试流程说明 */}\n        <div className=\"bg-white rounded-lg shadow p-6 mt-6\">\n          <h2 className=\"text-lg font-semibold text-gray-900 mb-4\">测试流程</h2>\n          <div className=\"space-y-3\">\n            <div className=\"flex items-start\">\n              <div className=\"w-6 h-6 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center text-sm font-medium mr-3 mt-0.5\">\n                1\n              </div>\n              <div>\n                <div className=\"font-medium text-gray-900\">点击测试按钮</div>\n                <div className=\"text-sm text-gray-600\">系统将创建 Stripe 支付会话</div>\n              </div>\n            </div>\n            \n            <div className=\"flex items-start\">\n              <div className=\"w-6 h-6 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center text-sm font-medium mr-3 mt-0.5\">\n                2\n              </div>\n              <div>\n                <div className=\"font-medium text-gray-900\">跳转到 Stripe 支付页面</div>\n                <div className=\"text-sm text-gray-600\">使用上面的测试卡号进行支付</div>\n              </div>\n            </div>\n            \n            <div className=\"flex items-start\">\n              <div className=\"w-6 h-6 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center text-sm font-medium mr-3 mt-0.5\">\n                3\n              </div>\n              <div>\n                <div className=\"font-medium text-gray-900\">支付成功后返回</div>\n                <div className=\"text-sm text-gray-600\">查看卡密自动发放结果</div>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* 返回按钮 */}\n        <div className=\"flex justify-center mt-8\">\n          <Link href=\"/\">\n            <Button variant=\"outline\">\n              返回首页\n            </Button>\n          </Link>\n        </div>\n      </main>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;AAuEsC;;AArEtC;AACA;AACA;AACA;AAAA;AACA;AAAA;AAAA;AAAA;AACA;;;AAPA;;;;;;;AASe,SAAS;;IACtB,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,MAAM,YAAY;QAChB;YACE,QAAQ;YACR,aAAa;YACb,MAAM;QACR;QACA;YACE,QAAQ;YACR,aAAa;YACb,MAAM;QACR;QACA;YACE,QAAQ;YACR,aAAa;YACb,MAAM;QACR;QACA;YACE,QAAQ;YACR,aAAa;YACb,MAAM;QACR;QACA;YACE,QAAQ;YACR,aAAa;YACb,MAAM;QACR;KACD;IAED,MAAM,mBAAmB;QACvB,WAAW;QACX,SAAS;QACT,WAAW;QAEX,IAAI;YACF,SAAS;YACT,MAAM,WAAW,MAAM,MAAM,8BAA8B;gBACzD,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBACnB,OAAO;wBAAC;4BACN,WAAW;4BACX,UAAU;wBACZ;qBAAE;oBACF,OAAO;gBACT;YACF;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,KAAK,KAAK,IAAI;YAChC;YAEA,aAAa;YACb,MAAM,SAAS,MAAM,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD;YAE9B,IAAI,CAAC,QAAQ;gBACX,MAAM,IAAI,MAAM;YAClB;YAEA,WAAW;YAEX,sBAAsB;YACtB,MAAM,EAAE,OAAO,WAAW,EAAE,GAAG,MAAM,OAAO,kBAAkB,CAAC;gBAC7D,WAAW,KAAK,SAAS;YAC3B;YAEA,IAAI,aAAa;gBACf,MAAM,IAAI,MAAM,YAAY,OAAO;YACrC;QAEF,EAAE,OAAO,OAAY;YACnB,SAAS,MAAM,OAAO,IAAI;QAC5B,SAAU;YACR,WAAW;QACb;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,+JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAI,WAAU;8CAAkC;;;;;;;;;;;0CAI7D,6LAAC;gCAAI,WAAU;0CAAwB;;;;;;;;;;;;;;;;;;;;;;0BAO7C,6LAAC;gBAAK,WAAU;;kCACd,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,iNAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;kDACpB,6LAAC;wCAAG,WAAU;kDAAmC;;;;;;;;;;;;0CAEnD,6LAAC;gCAAE,WAAU;0CAAgB;;;;;;;;;;;;kCAM/B,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAA2C;;;;;;0CACzD,6LAAC;gCAAI,WAAU;0CACZ,UAAU,GAAG,CAAC,CAAC,MAAM,sBACpB,6LAAC;wCAAgB,WAAU;;0DACzB,6LAAC;;kEACC,6LAAC;wDAAI,WAAU;kEAAmC,KAAK,MAAM;;;;;;kEAC7D,6LAAC;wDAAI,WAAU;kEAAyB,KAAK,WAAW;;;;;;;;;;;;0DAE1D,6LAAC;gDAAI,WAAW,CAAC,sCAAsC,EACrD,KAAK,IAAI,KAAK,YACV,gCACA,2BACJ;0DACC,KAAK,IAAI,KAAK,YAAY,OAAO;;;;;;;uCAV5B;;;;;;;;;;0CAgBd,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAAyC;;;;;;kDACvD,6LAAC;wCAAG,WAAU;;0DACZ,6LAAC;;oDAAG;kEAAE,6LAAC;kEAAO;;;;;;oDAAa;;;;;;;0DAC3B,6LAAC;;oDAAG;kEAAE,6LAAC;kEAAO;;;;;;oDAAY;;;;;;;0DAC1B,6LAAC;;oDAAG;kEAAE,6LAAC;kEAAO;;;;;;oDAAa;;;;;;;0DAC3B,6LAAC;;oDAAG;kEAAE,6LAAC;kEAAO;;;;;;oDAAc;;;;;;;;;;;;;;;;;;;;;;;;;kCAMlC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAA2C;;;;;;0CAEzD,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;;0DACC,6LAAC;gDAAM,SAAQ;gDAAQ,WAAU;0DAA+C;;;;;;0DAGhF,6LAAC;gDACC,IAAG;gDACH,MAAK;gDACL,OAAO;gDACP,UAAU,CAAC,IAAM,SAAS,EAAE,MAAM,CAAC,KAAK;gDACxC,WAAU;gDACV,aAAY;;;;;;;;;;;;kDAIhB,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAG,WAAU;0DAAyC;;;;;;0DACvD,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAK,WAAU;kEAAgB;;;;;;kEAChC,6LAAC;wDAAK,WAAU;kEAA2B,CAAA,GAAA,sHAAA,CAAA,cAAW,AAAD,EAAE;;;;;;;;;;;;;;;;;;oCAI1D,uBACC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,uNAAA,CAAA,cAAW;gDAAC,WAAU;;;;;;0DACvB,6LAAC;gDAAK,WAAU;0DAAgB;;;;;;;;;;;;oCAInC,yBACC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,8NAAA,CAAA,cAAW;gDAAC,WAAU;;;;;;0DACvB,6LAAC;gDAAK,WAAU;0DAAkB;;;;;;;;;;;;kDAItC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,qIAAA,CAAA,SAAM;gDACL,SAAS;gDACT,UAAU;gDACV,WAAU;gDACV,MAAK;;kEAEL,6LAAC,qNAAA,CAAA,aAAU;wDAAC,WAAU;;;;;;oDACrB,UAAU,eAAe;;;;;;;0DAG5B,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC,+JAAA,CAAA,UAAI;oDAAC,MAAK;oDAAiB,WAAU;8DAA4C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAS1F,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAA2C;;;;;;0CACzD,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DAAkH;;;;;;0DAGjI,6LAAC;;kEACC,6LAAC;wDAAI,WAAU;kEAA4B;;;;;;kEAC3C,6LAAC;wDAAI,WAAU;kEAAwB;;;;;;;;;;;;;;;;;;kDAI3C,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DAAkH;;;;;;0DAGjI,6LAAC;;kEACC,6LAAC;wDAAI,WAAU;kEAA4B;;;;;;kEAC3C,6LAAC;wDAAI,WAAU;kEAAwB;;;;;;;;;;;;;;;;;;kDAI3C,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DAAkH;;;;;;0DAGjI,6LAAC;;kEACC,6LAAC;wDAAI,WAAU;kEAA4B;;;;;;kEAC3C,6LAAC;wDAAI,WAAU;kEAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAO/C,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,+JAAA,CAAA,UAAI;4BAAC,MAAK;sCACT,cAAA,6LAAC,qIAAA,CAAA,SAAM;gCAAC,SAAQ;0CAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQtC;GA/PwB;KAAA", "debugId": null}}]}