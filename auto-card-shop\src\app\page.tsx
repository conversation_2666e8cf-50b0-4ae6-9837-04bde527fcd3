'use client'

import { useEffect, useState } from 'react'
import { useSession } from 'next-auth/react'
import { useRouter } from 'next/navigation'
import Link from 'next/link'
import { Button } from '@/components/ui/button'
import { formatPrice } from '@/lib/utils'
import { ShoppingCart, User, LogIn } from 'lucide-react'

interface Product {
  id: string
  name: string
  description: string
  price: number
  image: string
  category: {
    name: string
  }
  _count: {
    cards: number
  }
}

interface Category {
  id: string
  name: string
  slug: string
  _count: {
    products: number
  }
}

export default function Home() {
  const { data: session } = useSession()
  const router = useRouter()
  const [products, setProducts] = useState<Product[]>([])
  const [categories, setCategories] = useState<Category[]>([])
  const [selectedCategory, setSelectedCategory] = useState<string>('')
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    fetchCategories()
    fetchProducts()
  }, [])

  const fetchCategories = async () => {
    try {
      const response = await fetch('/api/categories')
      const data = await response.json()
      setCategories(data)
    } catch (error) {
      console.error('获取分类失败:', error)
    }
  }

  const fetchProducts = async (categoryId?: string) => {
    try {
      const url = categoryId
        ? `/api/products?categoryId=${categoryId}`
        : '/api/products'
      const response = await fetch(url)
      const data = await response.json()
      setProducts(data)
    } catch (error) {
      console.error('获取商品失败:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleCategoryChange = (categoryId: string) => {
    setSelectedCategory(categoryId)
    setLoading(true)
    fetchProducts(categoryId || undefined)
  }

  const handleBuyNow = (productId: string) => {
    router.push(`/checkout?productId=${productId}&quantity=1`)
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* 导航栏 */}
      <nav className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <Link href="/" className="text-xl font-bold text-gray-900">
                自动发卡网站
              </Link>
            </div>

            <div className="flex items-center space-x-4">
              {session ? (
                <>
                  <Link href="/orders" className="text-gray-700 hover:text-gray-900">
                    我的订单
                  </Link>
                  {session.user.role === 'ADMIN' && (
                    <>
                      <Link href="/admin" className="text-blue-600 hover:text-blue-800">
                        管理后台
                      </Link>
                      <Link href="/test-purchase" className="text-green-600 hover:text-green-800">
                        测试购买
                      </Link>
                    </>
                  )}
                  <div className="flex items-center space-x-2">
                    <User className="w-4 h-4" />
                    <span className="text-sm text-gray-700">{session.user.username}</span>
                  </div>
                </>
              ) : (
                <Link href="/auth/signin">
                  <Button variant="outline" size="sm">
                    <LogIn className="w-4 h-4 mr-2" />
                    登录
                  </Button>
                </Link>
              )}
            </div>
          </div>
        </div>
      </nav>

      {/* 主内容 */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* 分类筛选 */}
        <div className="mb-8">
          <div className="flex flex-wrap gap-2">
            <Button
              variant={selectedCategory === '' ? 'default' : 'outline'}
              onClick={() => handleCategoryChange('')}
              size="sm"
            >
              全部商品
            </Button>
            {categories.map((category) => (
              <Button
                key={category.id}
                variant={selectedCategory === category.id ? 'default' : 'outline'}
                onClick={() => handleCategoryChange(category.id)}
                size="sm"
              >
                {category.name} ({category._count.products})
              </Button>
            ))}
          </div>
        </div>

        {/* 商品列表 */}
        {loading ? (
          <div className="text-center py-12">
            <div className="text-gray-500">加载中...</div>
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            {products.map((product) => (
              <div key={product.id} className="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow">
                <Link href={`/product/${product.id}`}>
                  {product.image && (
                    <img
                      src={product.image}
                      alt={product.name}
                      className="w-full h-48 object-cover cursor-pointer"
                    />
                  )}
                </Link>
                <div className="p-4">
                  <div className="text-sm text-gray-500 mb-1">{product.category.name}</div>
                  <Link href={`/product/${product.id}`}>
                    <h3 className="text-lg font-semibold text-gray-900 mb-2 cursor-pointer hover:text-blue-600">
                      {product.name}
                    </h3>
                  </Link>
                  {product.description && (
                    <p className="text-gray-600 text-sm mb-3 line-clamp-2">{product.description}</p>
                  )}
                  <div className="flex items-center justify-between">
                    <div className="text-lg font-bold text-blue-600">
                      {formatPrice(product.price)}
                    </div>
                    <div className="text-sm text-gray-500">
                      库存: {product._count.cards}
                    </div>
                  </div>
                  <div className="flex space-x-2 mt-3">
                    <Link href={`/product/${product.id}`} className="flex-1">
                      <Button variant="outline" className="w-full">
                        查看详情
                      </Button>
                    </Link>
                    <Button
                      className="flex-1"
                      disabled={product._count.cards === 0}
                      onClick={() => handleBuyNow(product.id)}
                    >
                      <ShoppingCart className="w-4 h-4 mr-2" />
                      {product._count.cards === 0 ? '缺货' : '购买'}
                    </Button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}

        {products.length === 0 && !loading && (
          <div className="text-center py-12">
            <div className="text-gray-500">暂无商品</div>
          </div>
        )}
      </main>
    </div>
  )
}
