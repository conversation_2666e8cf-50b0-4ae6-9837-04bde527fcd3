{"version": 3, "sources": [], "sections": [{"offset": {"line": 31, "column": 0}, "map": {"version": 3, "sources": ["file:///F:/zidongfaka/auto-card-shop/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n\nexport function formatPrice(price: number) {\n  return new Intl.NumberFormat('zh-CN', {\n    style: 'currency',\n    currency: 'USD',\n  }).format(price)\n}\n\nexport function formatDate(date: string | Date) {\n  return new Intl.DateTimeFormat('zh-CN', {\n    year: 'numeric',\n    month: '2-digit',\n    day: '2-digit',\n    hour: '2-digit',\n    minute: '2-digit',\n  }).format(new Date(date))\n}\n"], "names": [], "mappings": ";;;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAEO,SAAS,YAAY,KAAa;IACvC,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;QACpC,OAAO;QACP,UAAU;IACZ,GAAG,MAAM,CAAC;AACZ;AAEO,SAAS,WAAW,IAAmB;IAC5C,OAAO,IAAI,KAAK,cAAc,CAAC,SAAS;QACtC,MAAM;QACN,OAAO;QACP,KAAK;QACL,MAAM;QACN,QAAQ;IACV,GAAG,MAAM,CAAC,IAAI,KAAK;AACrB", "debugId": null}}, {"offset": {"line": 64, "column": 0}, "map": {"version": 3, "sources": ["file:///F:/zidongfaka/auto-card-shop/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50\",\n  {\n    variants: {\n      variant: {\n        default: \"bg-primary text-primary-foreground hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-destructive-foreground hover:bg-destructive/90\",\n        outline:\n          \"border border-input bg-background hover:bg-accent hover:text-accent-foreground\",\n        secondary:\n          \"bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n        ghost: \"hover:bg-accent hover:text-accent-foreground\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-10 px-4 py-2\",\n        sm: \"h-9 rounded-md px-3\",\n        lg: \"h-11 rounded-md px-8\",\n        icon: \"h-10 w-10\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nexport interface ButtonProps\n  extends React.ButtonHTMLAttributes<HTMLButtonElement>,\n    VariantProps<typeof buttonVariants> {\n  asChild?: boolean\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant, size, asChild = false, ...props }, ref) => {\n    return (\n      <button\n        className={cn(buttonVariants({ variant, size, className }))}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nButton.displayName = \"Button\"\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACvB,0RACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aACE;YACF,SACE;YACF,WACE;YACF,OAAO;YACP,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AASF,MAAM,uBAAS,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAC5B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,UAAU,KAAK,EAAE,GAAG,OAAO,EAAE;IACxD,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACxD,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;AAEF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 121, "column": 0}, "map": {"version": 3, "sources": ["file:///F:/zidongfaka/auto-card-shop/src/app/orders/%5BorderId%5D/cards/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useEffect, useState } from 'react'\nimport { useParams } from 'next/navigation'\nimport { Button } from '@/components/ui/button'\nimport { Copy, Download, ArrowLeft } from 'lucide-react'\nimport Link from 'next/link'\n\ninterface OrderCards {\n  orderId: string\n  email: string\n  totalAmount: number\n  status: string\n  createdAt: string\n  cards: Record<string, Array<{\n    id: string\n    cardData: string\n    usedAt: string\n  }>>\n}\n\nexport default function OrderCardsPage() {\n  const params = useParams()\n  const orderId = params.orderId as string\n  const [orderCards, setOrderCards] = useState<OrderCards | null>(null)\n  const [loading, setLoading] = useState(true)\n  const [error, setError] = useState('')\n  const [copiedCard, setCopiedCard] = useState('')\n\n  useEffect(() => {\n    if (orderId) {\n      fetchOrderCards()\n    }\n  }, [orderId])\n\n  const fetchOrderCards = async () => {\n    try {\n      const response = await fetch(`/api/orders/${orderId}/cards`)\n      const data = await response.json()\n\n      if (response.ok) {\n        setOrderCards(data)\n      } else {\n        setError(data.error || '获取卡密失败')\n      }\n    } catch (error) {\n      setError('获取卡密失败，请重试')\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const copyToClipboard = async (text: string, cardId: string) => {\n    try {\n      await navigator.clipboard.writeText(text)\n      setCopiedCard(cardId)\n      setTimeout(() => setCopiedCard(''), 2000)\n    } catch (error) {\n      console.error('复制失败:', error)\n    }\n  }\n\n  const downloadCards = () => {\n    if (!orderCards) return\n\n    let content = `订单号: ${orderCards.orderId}\\n`\n    content += `邮箱: ${orderCards.email}\\n`\n    content += `总金额: $${orderCards.totalAmount.toFixed(2)}\\n`\n    content += `购买时间: ${new Date(orderCards.createdAt).toLocaleString('zh-CN')}\\n\\n`\n\n    Object.entries(orderCards.cards).forEach(([productName, cards]) => {\n      content += `=== ${productName} ===\\n`\n      cards.forEach((card, index) => {\n        content += `${index + 1}. ${card.cardData}\\n`\n      })\n      content += '\\n'\n    })\n\n    const blob = new Blob([content], { type: 'text/plain;charset=utf-8' })\n    const url = URL.createObjectURL(blob)\n    const a = document.createElement('a')\n    a.href = url\n    a.download = `order-${orderId}-cards.txt`\n    document.body.appendChild(a)\n    a.click()\n    document.body.removeChild(a)\n    URL.revokeObjectURL(url)\n  }\n\n  if (loading) {\n    return (\n      <div className=\"min-h-screen bg-gray-50 flex items-center justify-center\">\n        <div className=\"text-gray-500\">加载中...</div>\n      </div>\n    )\n  }\n\n  if (error) {\n    return (\n      <div className=\"min-h-screen bg-gray-50 flex items-center justify-center\">\n        <div className=\"text-center\">\n          <div className=\"text-red-600 mb-4\">{error}</div>\n          <Link href=\"/orders\">\n            <Button variant=\"outline\">\n              <ArrowLeft className=\"w-4 h-4 mr-2\" />\n              返回订单查询\n            </Button>\n          </Link>\n        </div>\n      </div>\n    )\n  }\n\n  if (!orderCards) {\n    return (\n      <div className=\"min-h-screen bg-gray-50 flex items-center justify-center\">\n        <div className=\"text-gray-500\">未找到订单信息</div>\n      </div>\n    )\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      {/* 导航栏 */}\n      <nav className=\"bg-white shadow-sm border-b\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"flex justify-between items-center h-16\">\n            <div className=\"flex items-center\">\n              <a href=\"/\" className=\"text-xl font-bold text-gray-900\">\n                自动发卡网站\n              </a>\n            </div>\n            <div className=\"text-sm text-gray-600\">\n              订单卡密\n            </div>\n          </div>\n        </div>\n      </nav>\n\n      <main className=\"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n        <div className=\"bg-white rounded-lg shadow p-6\">\n          {/* 订单信息 */}\n          <div className=\"mb-6\">\n            <div className=\"flex items-center justify-between mb-4\">\n              <h1 className=\"text-2xl font-bold text-gray-900\">订单卡密</h1>\n              <div className=\"flex space-x-3\">\n                <Button onClick={downloadCards} variant=\"outline\">\n                  <Download className=\"w-4 h-4 mr-2\" />\n                  下载卡密\n                </Button>\n                <Link href=\"/orders\">\n                  <Button variant=\"outline\">\n                    <ArrowLeft className=\"w-4 h-4 mr-2\" />\n                    返回\n                  </Button>\n                </Link>\n              </div>\n            </div>\n            \n            <div className=\"bg-gray-50 rounded-lg p-4 space-y-2 text-sm\">\n              <div className=\"flex justify-between\">\n                <span className=\"text-gray-600\">订单号:</span>\n                <span className=\"font-mono\">{orderCards.orderId}</span>\n              </div>\n              <div className=\"flex justify-between\">\n                <span className=\"text-gray-600\">邮箱:</span>\n                <span>{orderCards.email}</span>\n              </div>\n              <div className=\"flex justify-between\">\n                <span className=\"text-gray-600\">总金额:</span>\n                <span className=\"font-bold text-blue-600\">${orderCards.totalAmount.toFixed(2)}</span>\n              </div>\n              <div className=\"flex justify-between\">\n                <span className=\"text-gray-600\">购买时间:</span>\n                <span>{new Date(orderCards.createdAt).toLocaleString('zh-CN')}</span>\n              </div>\n            </div>\n          </div>\n\n          {/* 卡密列表 */}\n          <div className=\"space-y-6\">\n            {Object.entries(orderCards.cards).map(([productName, cards]) => (\n              <div key={productName} className=\"border border-gray-200 rounded-lg p-4\">\n                <h2 className=\"text-lg font-semibold text-gray-900 mb-4\">{productName}</h2>\n                <div className=\"space-y-3\">\n                  {cards.map((card, index) => (\n                    <div key={card.id} className=\"flex items-center justify-between bg-gray-50 rounded-lg p-3\">\n                      <div className=\"flex-1\">\n                        <div className=\"text-sm text-gray-600 mb-1\">卡密 #{index + 1}</div>\n                        <div className=\"font-mono text-lg text-gray-900 break-all\">\n                          {card.cardData}\n                        </div>\n                      </div>\n                      <Button\n                        onClick={() => copyToClipboard(card.cardData, card.id)}\n                        variant=\"outline\"\n                        size=\"sm\"\n                        className=\"ml-3\"\n                      >\n                        <Copy className=\"w-4 h-4 mr-1\" />\n                        {copiedCard === card.id ? '已复制' : '复制'}\n                      </Button>\n                    </div>\n                  ))}\n                </div>\n              </div>\n            ))}\n          </div>\n\n          {/* 使用说明 */}\n          <div className=\"mt-8 bg-blue-50 border border-blue-200 rounded-lg p-4\">\n            <h3 className=\"text-sm font-medium text-blue-900 mb-2\">使用说明</h3>\n            <ul className=\"text-sm text-blue-800 space-y-1\">\n              <li>• 请妥善保管您的卡密信息，避免泄露给他人</li>\n              <li>• 卡密一旦使用后无法退换，请确认后再使用</li>\n              <li>• 如有任何问题，请联系客服</li>\n              <li>• 建议下载保存卡密信息以备后用</li>\n            </ul>\n          </div>\n        </div>\n      </main>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AACA;AANA;;;;;;;AAqBe,SAAS;IACtB,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,UAAU,OAAO,OAAO;IAC9B,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAqB;IAChE,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,SAAS;YACX;QACF;IACF,GAAG;QAAC;KAAQ;IAEZ,MAAM,kBAAkB;QACtB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,CAAC,YAAY,EAAE,QAAQ,MAAM,CAAC;YAC3D,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,SAAS,EAAE,EAAE;gBACf,cAAc;YAChB,OAAO;gBACL,SAAS,KAAK,KAAK,IAAI;YACzB;QACF,EAAE,OAAO,OAAO;YACd,SAAS;QACX,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,kBAAkB,OAAO,MAAc;QAC3C,IAAI;YACF,MAAM,UAAU,SAAS,CAAC,SAAS,CAAC;YACpC,cAAc;YACd,WAAW,IAAM,cAAc,KAAK;QACtC,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,SAAS;QACzB;IACF;IAEA,MAAM,gBAAgB;QACpB,IAAI,CAAC,YAAY;QAEjB,IAAI,UAAU,CAAC,KAAK,EAAE,WAAW,OAAO,CAAC,EAAE,CAAC;QAC5C,WAAW,CAAC,IAAI,EAAE,WAAW,KAAK,CAAC,EAAE,CAAC;QACtC,WAAW,CAAC,MAAM,EAAE,WAAW,WAAW,CAAC,OAAO,CAAC,GAAG,EAAE,CAAC;QACzD,WAAW,CAAC,MAAM,EAAE,IAAI,KAAK,WAAW,SAAS,EAAE,cAAc,CAAC,SAAS,IAAI,CAAC;QAEhF,OAAO,OAAO,CAAC,WAAW,KAAK,EAAE,OAAO,CAAC,CAAC,CAAC,aAAa,MAAM;YAC5D,WAAW,CAAC,IAAI,EAAE,YAAY,MAAM,CAAC;YACrC,MAAM,OAAO,CAAC,CAAC,MAAM;gBACnB,WAAW,GAAG,QAAQ,EAAE,EAAE,EAAE,KAAK,QAAQ,CAAC,EAAE,CAAC;YAC/C;YACA,WAAW;QACb;QAEA,MAAM,OAAO,IAAI,KAAK;YAAC;SAAQ,EAAE;YAAE,MAAM;QAA2B;QACpE,MAAM,MAAM,IAAI,eAAe,CAAC;QAChC,MAAM,IAAI,SAAS,aAAa,CAAC;QACjC,EAAE,IAAI,GAAG;QACT,EAAE,QAAQ,GAAG,CAAC,MAAM,EAAE,QAAQ,UAAU,CAAC;QACzC,SAAS,IAAI,CAAC,WAAW,CAAC;QAC1B,EAAE,KAAK;QACP,SAAS,IAAI,CAAC,WAAW,CAAC;QAC1B,IAAI,eAAe,CAAC;IACtB;IAEA,IAAI,SAAS;QACX,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;0BAAgB;;;;;;;;;;;IAGrC;IAEA,IAAI,OAAO;QACT,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCAAqB;;;;;;kCACpC,8OAAC,4JAAA,CAAA,UAAI;wBAAC,MAAK;kCACT,cAAA,8OAAC,kIAAA,CAAA,SAAM;4BAAC,SAAQ;;8CACd,8OAAC,gNAAA,CAAA,YAAS;oCAAC,WAAU;;;;;;gCAAiB;;;;;;;;;;;;;;;;;;;;;;;IAOlD;IAEA,IAAI,CAAC,YAAY;QACf,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;0BAAgB;;;;;;;;;;;IAGrC;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAE,MAAK;oCAAI,WAAU;8CAAkC;;;;;;;;;;;0CAI1D,8OAAC;gCAAI,WAAU;0CAAwB;;;;;;;;;;;;;;;;;;;;;;0BAO7C,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAAmC;;;;;;sDACjD,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,kIAAA,CAAA,SAAM;oDAAC,SAAS;oDAAe,SAAQ;;sEACtC,8OAAC,0MAAA,CAAA,WAAQ;4DAAC,WAAU;;;;;;wDAAiB;;;;;;;8DAGvC,8OAAC,4JAAA,CAAA,UAAI;oDAAC,MAAK;8DACT,cAAA,8OAAC,kIAAA,CAAA,SAAM;wDAAC,SAAQ;;0EACd,8OAAC,gNAAA,CAAA,YAAS;gEAAC,WAAU;;;;;;4DAAiB;;;;;;;;;;;;;;;;;;;;;;;;8CAO9C,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAK,WAAU;8DAAgB;;;;;;8DAChC,8OAAC;oDAAK,WAAU;8DAAa,WAAW,OAAO;;;;;;;;;;;;sDAEjD,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAK,WAAU;8DAAgB;;;;;;8DAChC,8OAAC;8DAAM,WAAW,KAAK;;;;;;;;;;;;sDAEzB,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAK,WAAU;8DAAgB;;;;;;8DAChC,8OAAC;oDAAK,WAAU;;wDAA0B;wDAAE,WAAW,WAAW,CAAC,OAAO,CAAC;;;;;;;;;;;;;sDAE7E,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAK,WAAU;8DAAgB;;;;;;8DAChC,8OAAC;8DAAM,IAAI,KAAK,WAAW,SAAS,EAAE,cAAc,CAAC;;;;;;;;;;;;;;;;;;;;;;;;sCAM3D,8OAAC;4BAAI,WAAU;sCACZ,OAAO,OAAO,CAAC,WAAW,KAAK,EAAE,GAAG,CAAC,CAAC,CAAC,aAAa,MAAM,iBACzD,8OAAC;oCAAsB,WAAU;;sDAC/B,8OAAC;4CAAG,WAAU;sDAA4C;;;;;;sDAC1D,8OAAC;4CAAI,WAAU;sDACZ,MAAM,GAAG,CAAC,CAAC,MAAM,sBAChB,8OAAC;oDAAkB,WAAU;;sEAC3B,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAI,WAAU;;wEAA6B;wEAAK,QAAQ;;;;;;;8EACzD,8OAAC;oEAAI,WAAU;8EACZ,KAAK,QAAQ;;;;;;;;;;;;sEAGlB,8OAAC,kIAAA,CAAA,SAAM;4DACL,SAAS,IAAM,gBAAgB,KAAK,QAAQ,EAAE,KAAK,EAAE;4DACrD,SAAQ;4DACR,MAAK;4DACL,WAAU;;8EAEV,8OAAC,kMAAA,CAAA,OAAI;oEAAC,WAAU;;;;;;gEACf,eAAe,KAAK,EAAE,GAAG,QAAQ;;;;;;;;mDAd5B,KAAK,EAAE;;;;;;;;;;;mCAJb;;;;;;;;;;sCA4Bd,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAyC;;;;;;8CACvD,8OAAC;oCAAG,WAAU;;sDACZ,8OAAC;sDAAG;;;;;;sDACJ,8OAAC;sDAAG;;;;;;sDACJ,8OAAC;sDAAG;;;;;;sDACJ,8OAAC;sDAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOlB", "debugId": null}}]}