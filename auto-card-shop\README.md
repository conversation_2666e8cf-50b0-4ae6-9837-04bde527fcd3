# 自动发卡网站

基于 Next.js 15 和 Stripe 支付的自动发卡系统，支持数字商品的在线销售和自动交付。

## 功能特性

### 🛒 前端功能
- 🛍️ **商品展示**: 分类浏览、商品搜索、库存显示
- 💳 **在线支付**: 集成 Stripe 支付网关，支持信用卡支付
- 🎫 **自动发卡**: 支付成功后自动发放卡密
- 👤 **用户系统**: 用户注册、登录、订单查询
- 📱 **响应式设计**: 支持桌面和移动设备

### 🔧 管理后台
- 📊 **仪表板**: 实时统计数据、最近订单、系统信息
- 📁 **分类管理**: 创建、编辑、删除商品分类
- 🛍️ **商品管理**: 商品 CRUD、库存管理、状态控制
- 📦 **订单管理**: 订单查询、状态更新、数据导出
- 🎫 **卡密管理**: 批量添加、状态筛选、库存统计
- 👥 **用户管理**: 用户列表、角色管理、权限控制
- ⚙️ **系统设置**: 支付配置、邮件设置、安全策略

### 🔒 安全特性
- 🔐 **身份认证**: NextAuth.js + JWT 认证
- 🛡️ **权限控制**: 基于角色的访问控制
- 🔒 **数据加密**: 密码 bcrypt 加密
- 🚫 **输入验证**: 前后端数据验证
- 📝 **操作日志**: 关键操作记录

## 技术栈

- **前端**: Next.js 15, React 19, TypeScript, Tailwind CSS
- **后端**: Next.js API Routes, Prisma ORM
- **数据库**: SQLite (可切换到 PostgreSQL/MySQL)
- **支付**: Stripe
- **认证**: NextAuth.js
- **UI组件**: Radix UI, Lucide React

## 快速开始

### 1. 克隆项目

```bash
git clone <repository-url>
cd auto-card-shop
```

### 2. 安装依赖

```bash
npm install
```

### 3. 环境配置

复制 `.env` 文件并配置必要的环境变量：

```bash
# 数据库
DATABASE_URL="file:./dev.db"

# NextAuth
NEXTAUTH_SECRET="your-secret-key-here-change-this-in-production"
NEXTAUTH_URL="http://localhost:3000"

# Stripe
STRIPE_PUBLISHABLE_KEY="pk_test_your_stripe_publishable_key"
STRIPE_SECRET_KEY="sk_test_your_stripe_secret_key"
STRIPE_WEBHOOK_SECRET="whsec_your_webhook_secret"
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY="pk_test_your_stripe_publishable_key"

# 应用设置
APP_NAME="自动发卡网站"
APP_URL="http://localhost:3000"
```

### 4. 数据库设置

```bash
# 生成 Prisma 客户端
npx prisma generate

# 运行数据库迁移
npx prisma migrate dev

# 填充示例数据
npm run db:seed
```

### 5. 启动开发服务器

```bash
npm run dev
```

访问 [http://localhost:3000](http://localhost:3000) 查看网站。

## 默认账户

系统会自动创建一个管理员账户：

- **邮箱**: <EMAIL>
- **密码**: admin123
- **角色**: 管理员

## Stripe 沙箱测试

项目已配置 Stripe 测试环境，可以进行完整的支付测试：

### 测试页面
- **Stripe 测试**: http://localhost:3000/stripe-test
- **免支付测试**: http://localhost:3000/test-purchase

### 测试卡号
- **成功支付**: `****************`
- **卡片被拒绝**: `****************`
- **资金不足**: `****************`
- **过期日期**: 任何未来日期（如 12/34）
- **CVC**: 任何3位数字（如 123）

详细测试指南请查看 [STRIPE_TEST_GUIDE.md](./STRIPE_TEST_GUIDE.md)

## 主要页面

### 前端页面
- `/` - 首页（商品展示）
- `/auth/signin` - 用户登录
- `/auth/signup` - 用户注册
- `/orders` - 订单查询
- `/orders/[orderId]/cards` - 查看卡密

### 管理后台
- `/admin` - 仪表板
- `/admin/categories` - 分类管理
- `/admin/products` - 商品管理
- `/admin/orders` - 订单管理
- `/admin/cards` - 卡密管理
- `/admin/users` - 用户管理
- `/admin/settings` - 系统设置

## API 接口

### 认证相关
- `POST /api/auth/register` - 用户注册
- `GET/POST /api/auth/[...nextauth]` - NextAuth 认证

### 商品相关
- `GET /api/products` - 获取商品列表
- `POST /api/products` - 创建商品（管理员）
- `GET /api/categories` - 获取分类列表
- `POST /api/categories` - 创建分类（管理员）

### 订单相关
- `GET /api/orders` - 获取订单列表
- `GET /api/orders/[orderId]/cards` - 获取订单卡密

### 卡密相关
- `GET /api/cards` - 获取卡密列表（管理员）
- `POST /api/cards` - 批量添加卡密（管理员）
- `DELETE /api/cards/bulk-delete` - 批量删除卡密（管理员）

### 用户管理
- `GET /api/admin/users` - 获取用户列表（管理员）
- `PATCH /api/admin/users/[userId]` - 更新用户角色（管理员）
- `DELETE /api/admin/users/[userId]` - 删除用户（管理员）

### 分类管理
- `GET /api/admin/categories` - 获取所有分类（管理员）
- `PUT /api/admin/categories/[categoryId]` - 更新分类（管理员）
- `DELETE /api/admin/categories/[categoryId]` - 删除分类（管理员）

### 支付相关
- `POST /api/payment/create-intent` - 创建支付意图
- `POST /api/webhooks/stripe` - Stripe Webhook

## 部署说明

### 环境变量配置

生产环境需要配置以下环境变量：

1. **数据库**: 使用 PostgreSQL 或 MySQL
2. **Stripe**: 使用生产环境的 API 密钥
3. **NextAuth**: 设置强密码和正确的 URL
4. **Webhook**: 配置 Stripe Webhook 端点

### Vercel 部署

1. 推送代码到 GitHub
2. 在 Vercel 中导入项目
3. 配置环境变量
4. 部署

### Docker 部署

```bash
# 构建镜像
docker build -t auto-card-shop .

# 运行容器
docker run -p 3000:3000 auto-card-shop
```

## 管理后台使用指南

### 首次设置

1. **登录管理后台**
   - 使用默认管理员账户登录：<EMAIL> / admin123
   - 访问 `/admin` 进入管理后台

2. **创建商品分类**
   - 进入"分类管理"页面
   - 点击"添加分类"创建新分类
   - 设置分类名称、URL标识符和描述

3. **添加商品**
   - 进入"商品管理"页面
   - 点击"添加商品"创建新商品
   - 填写商品信息：名称、价格、分类、描述等

4. **批量添加卡密**
   - 进入"卡密管理"页面
   - 选择对应商品
   - 批量粘贴卡密内容（每行一个）
   - 系统会自动更新商品库存

### 日常管理

1. **订单处理**
   - 在"订单管理"中查看所有订单
   - 支付成功的订单会自动发卡
   - 可手动更改订单状态

2. **库存管理**
   - 在"卡密管理"中查看库存状态
   - 支持按商品和状态筛选
   - 可批量删除已使用的卡密

3. **用户管理**
   - 查看所有注册用户
   - 设置用户角色（普通用户/管理员）
   - 删除违规用户

### 数据导出

- **订单导出**: 在订单管理页面点击"导出订单"
- **用户导出**: 在用户管理页面点击"导出用户"
- **卡密导出**: 在卡密管理页面点击"导出卡密"

## 开发指南

### 配置 Stripe

1. 注册 Stripe 账户
2. 获取 API 密钥
3. 配置 Webhook 端点: `https://your-domain.com/api/webhooks/stripe`
4. 选择事件: `payment_intent.succeeded`

### 自定义样式

项目使用 Tailwind CSS，可以在以下文件中自定义样式：

- `src/app/globals.css` - 全局样式
- `tailwind.config.js` - Tailwind 配置
- `src/components/ui/` - UI 组件样式

## 常见问题

### Q: 如何更换数据库？

A: 修改 `prisma/schema.prisma` 中的 `datasource` 配置，然后运行 `npx prisma migrate dev`。

### Q: 如何添加新的支付方式？

A: 可以在 Stripe 中配置其他支付方式，或者集成其他支付网关。

### Q: 如何自定义邮件通知？

A: 在 `.env` 中配置 SMTP 设置，然后在支付成功后发送邮件。

## 许可证

MIT License

## 支持

如有问题，请提交 Issue 或联系开发者。
