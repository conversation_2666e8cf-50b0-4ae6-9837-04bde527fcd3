# 自动发卡网站

基于 Next.js 15 和 Stripe 支付的自动发卡系统，支持数字商品的在线销售和自动交付。

## 功能特性

- 🛍️ **商品管理**: 支持商品分类、库存管理、价格设置
- 💳 **Stripe 支付**: 集成 Stripe 支付网关，支持信用卡支付
- 🎫 **自动发卡**: 支付成功后自动发放卡密
- 👤 **用户系统**: 用户注册、登录、订单查询
- 🔐 **管理后台**: 管理员可管理商品、订单、卡密
- 📱 **响应式设计**: 支持桌面和移动设备
- 🔒 **安全可靠**: 密码加密、JWT 认证、数据验证

## 技术栈

- **前端**: Next.js 15, React 19, TypeScript, Tailwind CSS
- **后端**: Next.js API Routes, Prisma ORM
- **数据库**: SQLite (可切换到 PostgreSQL/MySQL)
- **支付**: Stripe
- **认证**: NextAuth.js
- **UI组件**: Radix UI, Lucide React

## 快速开始

### 1. 克隆项目

```bash
git clone <repository-url>
cd auto-card-shop
```

### 2. 安装依赖

```bash
npm install
```

### 3. 环境配置

复制 `.env` 文件并配置必要的环境变量：

```bash
# 数据库
DATABASE_URL="file:./dev.db"

# NextAuth
NEXTAUTH_SECRET="your-secret-key-here-change-this-in-production"
NEXTAUTH_URL="http://localhost:3000"

# Stripe
STRIPE_PUBLISHABLE_KEY="pk_test_your_stripe_publishable_key"
STRIPE_SECRET_KEY="sk_test_your_stripe_secret_key"
STRIPE_WEBHOOK_SECRET="whsec_your_webhook_secret"
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY="pk_test_your_stripe_publishable_key"

# 应用设置
APP_NAME="自动发卡网站"
APP_URL="http://localhost:3000"
```

### 4. 数据库设置

```bash
# 生成 Prisma 客户端
npx prisma generate

# 运行数据库迁移
npx prisma migrate dev

# 填充示例数据
npm run db:seed
```

### 5. 启动开发服务器

```bash
npm run dev
```

访问 [http://localhost:3000](http://localhost:3000) 查看网站。

## 默认账户

系统会自动创建一个管理员账户：

- **邮箱**: <EMAIL>
- **密码**: admin123
- **角色**: 管理员

## 主要页面

- `/` - 首页（商品展示）
- `/auth/signin` - 用户登录
- `/auth/signup` - 用户注册
- `/orders` - 订单查询
- `/orders/[orderId]/cards` - 查看卡密
- `/admin` - 管理后台（仅管理员）

## API 接口

### 认证相关
- `POST /api/auth/register` - 用户注册
- `GET/POST /api/auth/[...nextauth]` - NextAuth 认证

### 商品相关
- `GET /api/products` - 获取商品列表
- `POST /api/products` - 创建商品（管理员）
- `GET /api/categories` - 获取分类列表
- `POST /api/categories` - 创建分类（管理员）

### 订单相关
- `GET /api/orders` - 获取订单列表
- `GET /api/orders/[orderId]/cards` - 获取订单卡密

### 卡密相关
- `GET /api/cards` - 获取卡密列表（管理员）
- `POST /api/cards` - 批量添加卡密（管理员）

### 支付相关
- `POST /api/payment/create-intent` - 创建支付意图
- `POST /api/webhooks/stripe` - Stripe Webhook

## 部署说明

### 环境变量配置

生产环境需要配置以下环境变量：

1. **数据库**: 使用 PostgreSQL 或 MySQL
2. **Stripe**: 使用生产环境的 API 密钥
3. **NextAuth**: 设置强密码和正确的 URL
4. **Webhook**: 配置 Stripe Webhook 端点

### Vercel 部署

1. 推送代码到 GitHub
2. 在 Vercel 中导入项目
3. 配置环境变量
4. 部署

### Docker 部署

```bash
# 构建镜像
docker build -t auto-card-shop .

# 运行容器
docker run -p 3000:3000 auto-card-shop
```

## 开发指南

### 添加新商品

1. 登录管理后台
2. 进入商品管理
3. 创建商品分类（如果需要）
4. 添加商品信息
5. 批量上传卡密

### 配置 Stripe

1. 注册 Stripe 账户
2. 获取 API 密钥
3. 配置 Webhook 端点: `https://your-domain.com/api/webhooks/stripe`
4. 选择事件: `payment_intent.succeeded`

### 自定义样式

项目使用 Tailwind CSS，可以在以下文件中自定义样式：

- `src/app/globals.css` - 全局样式
- `tailwind.config.js` - Tailwind 配置
- `src/components/ui/` - UI 组件样式

## 常见问题

### Q: 如何更换数据库？

A: 修改 `prisma/schema.prisma` 中的 `datasource` 配置，然后运行 `npx prisma migrate dev`。

### Q: 如何添加新的支付方式？

A: 可以在 Stripe 中配置其他支付方式，或者集成其他支付网关。

### Q: 如何自定义邮件通知？

A: 在 `.env` 中配置 SMTP 设置，然后在支付成功后发送邮件。

## 许可证

MIT License

## 支持

如有问题，请提交 Issue 或联系开发者。
