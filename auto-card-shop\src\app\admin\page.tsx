'use client'

import { useEffect, useState } from 'react'
import { Package, ShoppingCart, CreditCard, Users } from 'lucide-react'

interface Stats {
  totalProducts: number
  totalOrders: number
  totalCards: number
  totalUsers: number
  recentOrders: any[]
}

export default function AdminDashboard() {
  const [stats, setStats] = useState<Stats>({
    totalProducts: 0,
    totalOrders: 0,
    totalCards: 0,
    totalUsers: 0,
    recentOrders: []
  })
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    fetchStats()
  }, [])

  const fetchStats = async () => {
    try {
      // 获取实时统计数据
      const [productsRes, ordersRes, cardsRes, usersRes] = await Promise.all([
        fetch('/api/products?status=all'),
        fetch('/api/orders'),
        fetch('/api/cards'),
        fetch('/api/admin/users')
      ])

      const products = await productsRes.json()
      const orders = await ordersRes.json()
      const cards = await cardsRes.json()
      const users = await usersRes.json()

      setStats({
        totalProducts: products.length || 0,
        totalOrders: orders.length || 0,
        totalCards: cards.length || 0,
        totalUsers: users.length || 0,
        recentOrders: orders.slice(0, 5) || []
      })
    } catch (error) {
      console.error('获取统计数据失败:', error)
      // 使用默认数据
      setStats({
        totalProducts: 0,
        totalOrders: 0,
        totalCards: 0,
        totalUsers: 0,
        recentOrders: []
      })
    } finally {
      setLoading(false)
    }
  }

  const statCards = [
    {
      title: '商品总数',
      value: stats.totalProducts,
      icon: Package,
      color: 'bg-blue-500'
    },
    {
      title: '订单总数',
      value: stats.totalOrders,
      icon: ShoppingCart,
      color: 'bg-green-500'
    },
    {
      title: '卡密总数',
      value: stats.totalCards,
      icon: CreditCard,
      color: 'bg-purple-500'
    },
    {
      title: '用户总数',
      value: stats.totalUsers,
      icon: Users,
      color: 'bg-orange-500'
    }
  ]

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-gray-500">加载中...</div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-2xl font-bold text-gray-900">仪表板</h1>
        <p className="text-gray-600">欢迎来到管理后台</p>
      </div>

      {/* 统计卡片 */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {statCards.map((card, index) => {
          const Icon = card.icon
          return (
            <div key={index} className="bg-white rounded-lg shadow p-6">
              <div className="flex items-center">
                <div className={`${card.color} rounded-lg p-3`}>
                  <Icon className="w-6 h-6 text-white" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">{card.title}</p>
                  <p className="text-2xl font-bold text-gray-900">{card.value}</p>
                </div>
              </div>
            </div>
          )
        })}
      </div>

      {/* 快速操作 */}
      <div className="bg-white rounded-lg shadow p-6">
        <h2 className="text-lg font-semibold text-gray-900 mb-4">快速操作</h2>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <a
            href="/admin/products"
            className="p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"
          >
            <Package className="w-8 h-8 text-blue-500 mb-2" />
            <h3 className="font-medium text-gray-900">管理商品</h3>
            <p className="text-sm text-gray-600">添加、编辑或删除商品</p>
          </a>
          
          <a
            href="/admin/cards"
            className="p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"
          >
            <CreditCard className="w-8 h-8 text-purple-500 mb-2" />
            <h3 className="font-medium text-gray-900">管理卡密</h3>
            <p className="text-sm text-gray-600">批量添加或查看卡密</p>
          </a>
          
          <a
            href="/admin/orders"
            className="p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"
          >
            <ShoppingCart className="w-8 h-8 text-green-500 mb-2" />
            <h3 className="font-medium text-gray-900">查看订单</h3>
            <p className="text-sm text-gray-600">管理和处理订单</p>
          </a>
        </div>
      </div>

      {/* 最近订单 */}
      {stats.recentOrders.length > 0 && (
        <div className="bg-white rounded-lg shadow p-6">
          <h2 className="text-lg font-semibold text-gray-900 mb-4">最近订单</h2>
          <div className="space-y-3">
            {stats.recentOrders.map((order: any) => (
              <div key={order.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <div>
                  <div className="text-sm font-medium text-gray-900">
                    订单 #{order.id.slice(0, 8)}...
                  </div>
                  <div className="text-xs text-gray-500">{order.email}</div>
                </div>
                <div className="text-right">
                  <div className="text-sm font-medium text-blue-600">
                    ${order.totalAmount.toFixed(2)}
                  </div>
                  <div className="text-xs text-gray-500">
                    {new Date(order.createdAt).toLocaleDateString('zh-CN')}
                  </div>
                </div>
              </div>
            ))}
          </div>
          <div className="mt-4">
            <a
              href="/admin/orders"
              className="text-sm text-blue-600 hover:text-blue-800"
            >
              查看所有订单 →
            </a>
          </div>
        </div>
      )}

      {/* 系统信息 */}
      <div className="bg-white rounded-lg shadow p-6">
        <h2 className="text-lg font-semibold text-gray-900 mb-4">系统信息</h2>
        <div className="space-y-2 text-sm">
          <div className="flex justify-between">
            <span className="text-gray-600">系统版本:</span>
            <span className="text-gray-900">v1.0.0</span>
          </div>
          <div className="flex justify-between">
            <span className="text-gray-600">数据库:</span>
            <span className="text-gray-900">SQLite</span>
          </div>
          <div className="flex justify-between">
            <span className="text-gray-600">支付网关:</span>
            <span className="text-gray-900">Stripe</span>
          </div>
          <div className="flex justify-between">
            <span className="text-gray-600">最后更新:</span>
            <span className="text-gray-900">{new Date().toLocaleString('zh-CN')}</span>
          </div>
        </div>
      </div>
    </div>
  )
}
