import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'

// 模拟 Stripe Webhook 事件（仅开发环境）
export async function POST(request: NextRequest) {
  if (process.env.NODE_ENV !== 'development') {
    return NextResponse.json(
      { error: '此接口仅在开发环境可用' },
      { status: 403 }
    )
  }

  try {
    const { sessionId, orderId } = await request.json()

    if (!sessionId || !orderId) {
      return NextResponse.json(
        { error: '缺少必需参数' },
        { status: 400 }
      )
    }

    // 模拟 checkout.session.completed 事件
    const mockEvent = {
      type: 'checkout.session.completed',
      data: {
        object: {
          id: sessionId,
          metadata: {
            orderId: orderId
          },
          payment_status: 'paid',
          customer_email: '<EMAIL>'
        }
      }
    }

    // 调用 webhook 处理逻辑
    const webhookResponse = await fetch(`${process.env.NEXTAUTH_URL}/api/webhooks/stripe`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(mockEvent)
    })

    if (webhookResponse.ok) {
      return NextResponse.json({
        message: 'Webhook 事件模拟成功',
        event: mockEvent
      })
    } else {
      const error = await webhookResponse.text()
      return NextResponse.json(
        { error: `Webhook 处理失败: ${error}` },
        { status: 500 }
      )
    }
  } catch (error) {
    console.error('模拟 Webhook 错误:', error)
    return NextResponse.json(
      { error: '服务器错误' },
      { status: 500 }
    )
  }
}
