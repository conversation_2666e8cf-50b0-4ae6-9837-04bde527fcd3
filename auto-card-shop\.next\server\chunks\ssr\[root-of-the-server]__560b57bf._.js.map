{"version": 3, "sources": [], "sections": [{"offset": {"line": 31, "column": 0}, "map": {"version": 3, "sources": ["file:///F:/zidongfaka/auto-card-shop/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n\nexport function formatPrice(price: number) {\n  return new Intl.NumberFormat('zh-CN', {\n    style: 'currency',\n    currency: 'USD',\n  }).format(price)\n}\n\nexport function formatDate(date: string | Date) {\n  return new Intl.DateTimeFormat('zh-CN', {\n    year: 'numeric',\n    month: '2-digit',\n    day: '2-digit',\n    hour: '2-digit',\n    minute: '2-digit',\n  }).format(new Date(date))\n}\n"], "names": [], "mappings": ";;;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAEO,SAAS,YAAY,KAAa;IACvC,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;QACpC,OAAO;QACP,UAAU;IACZ,GAAG,MAAM,CAAC;AACZ;AAEO,SAAS,WAAW,IAAmB;IAC5C,OAAO,IAAI,KAAK,cAAc,CAAC,SAAS;QACtC,MAAM;QACN,OAAO;QACP,KAAK;QACL,MAAM;QACN,QAAQ;IACV,GAAG,MAAM,CAAC,IAAI,KAAK;AACrB", "debugId": null}}, {"offset": {"line": 64, "column": 0}, "map": {"version": 3, "sources": ["file:///F:/zidongfaka/auto-card-shop/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50\",\n  {\n    variants: {\n      variant: {\n        default: \"bg-primary text-primary-foreground hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-destructive-foreground hover:bg-destructive/90\",\n        outline:\n          \"border border-input bg-background hover:bg-accent hover:text-accent-foreground\",\n        secondary:\n          \"bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n        ghost: \"hover:bg-accent hover:text-accent-foreground\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-10 px-4 py-2\",\n        sm: \"h-9 rounded-md px-3\",\n        lg: \"h-11 rounded-md px-8\",\n        icon: \"h-10 w-10\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nexport interface ButtonProps\n  extends React.ButtonHTMLAttributes<HTMLButtonElement>,\n    VariantProps<typeof buttonVariants> {\n  asChild?: boolean\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant, size, asChild = false, ...props }, ref) => {\n    return (\n      <button\n        className={cn(buttonVariants({ variant, size, className }))}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nButton.displayName = \"Button\"\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACvB,0RACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aACE;YACF,SACE;YACF,WACE;YACF,OAAO;YACP,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AASF,MAAM,uBAAS,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAC5B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,UAAU,KAAK,EAAE,GAAG,OAAO,EAAE;IACxD,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACxD,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;AAEF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 121, "column": 0}, "map": {"version": 3, "sources": ["file:///F:/zidongfaka/auto-card-shop/src/app/success/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useEffect, useState } from 'react'\nimport { useSearchParams } from 'next/navigation'\nimport { Button } from '@/components/ui/button'\nimport { CheckCircle, Copy, Download, Home } from 'lucide-react'\nimport Link from 'next/link'\n\ninterface OrderData {\n  orderId: string\n  email: string\n  totalAmount: number\n  status: string\n  createdAt: string\n  cards: Record<string, Array<{\n    id: string\n    cardData: string\n    usedAt: string\n  }>>\n}\n\nexport default function SuccessPage() {\n  const searchParams = useSearchParams()\n  const sessionId = searchParams.get('session_id')\n  \n  const [orderData, setOrderData] = useState<OrderData | null>(null)\n  const [loading, setLoading] = useState(true)\n  const [error, setError] = useState('')\n  const [copiedCard, setCopiedCard] = useState('')\n\n  useEffect(() => {\n    if (sessionId) {\n      fetchOrderData()\n    } else {\n      setError('缺少支付会话信息')\n      setLoading(false)\n    }\n  }, [sessionId])\n\n  const fetchOrderData = async () => {\n    try {\n      const response = await fetch(`/api/payment/success?session_id=${sessionId}`)\n      const data = await response.json()\n\n      if (response.ok) {\n        setOrderData(data)\n      } else {\n        setError(data.error || '获取订单信息失败')\n      }\n    } catch (error) {\n      setError('获取订单信息失败')\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const copyToClipboard = async (text: string, cardId: string) => {\n    try {\n      await navigator.clipboard.writeText(text)\n      setCopiedCard(cardId)\n      setTimeout(() => setCopiedCard(''), 2000)\n    } catch (error) {\n      console.error('复制失败:', error)\n    }\n  }\n\n  const downloadCards = () => {\n    if (!orderData) return\n\n    let content = `订单号: ${orderData.orderId}\\n`\n    content += `邮箱: ${orderData.email}\\n`\n    content += `总金额: $${orderData.totalAmount.toFixed(2)}\\n`\n    content += `购买时间: ${new Date(orderData.createdAt).toLocaleString('zh-CN')}\\n\\n`\n\n    Object.entries(orderData.cards).forEach(([productName, cards]) => {\n      content += `=== ${productName} ===\\n`\n      cards.forEach((card, index) => {\n        content += `${index + 1}. ${card.cardData}\\n`\n      })\n      content += '\\n'\n    })\n\n    const blob = new Blob([content], { type: 'text/plain;charset=utf-8' })\n    const url = URL.createObjectURL(blob)\n    const a = document.createElement('a')\n    a.href = url\n    a.download = `order-${orderData.orderId}-cards.txt`\n    document.body.appendChild(a)\n    a.click()\n    document.body.removeChild(a)\n    URL.revokeObjectURL(url)\n  }\n\n  if (loading) {\n    return (\n      <div className=\"min-h-screen bg-gray-50 flex items-center justify-center\">\n        <div className=\"text-gray-500\">处理中...</div>\n      </div>\n    )\n  }\n\n  if (error) {\n    return (\n      <div className=\"min-h-screen bg-gray-50 flex items-center justify-center\">\n        <div className=\"text-center\">\n          <div className=\"text-red-600 mb-4\">{error}</div>\n          <Link href=\"/\">\n            <Button variant=\"outline\">\n              <Home className=\"w-4 h-4 mr-2\" />\n              返回首页\n            </Button>\n          </Link>\n        </div>\n      </div>\n    )\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      {/* 导航栏 */}\n      <nav className=\"bg-white shadow-sm border-b\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"flex justify-between items-center h-16\">\n            <div className=\"flex items-center\">\n              <Link href=\"/\" className=\"text-xl font-bold text-gray-900\">\n                自动发卡网站\n              </Link>\n            </div>\n            <div className=\"text-sm text-gray-600\">\n              支付成功\n            </div>\n          </div>\n        </div>\n      </nav>\n\n      <main className=\"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n        {/* 成功提示 */}\n        <div className=\"bg-white rounded-lg shadow p-6 mb-6\">\n          <div className=\"flex items-center justify-center mb-4\">\n            <CheckCircle className=\"w-16 h-16 text-green-500\" />\n          </div>\n          <div className=\"text-center\">\n            <h1 className=\"text-2xl font-bold text-gray-900 mb-2\">支付成功！</h1>\n            <p className=\"text-gray-600\">您的订单已完成，卡密已自动发放</p>\n          </div>\n        </div>\n\n        {orderData && (\n          <>\n            {/* 订单信息 */}\n            <div className=\"bg-white rounded-lg shadow p-6 mb-6\">\n              <div className=\"flex justify-between items-center mb-4\">\n                <h2 className=\"text-lg font-semibold text-gray-900\">订单信息</h2>\n                <Button onClick={downloadCards} variant=\"outline\">\n                  <Download className=\"w-4 h-4 mr-2\" />\n                  下载卡密\n                </Button>\n              </div>\n              \n              <div className=\"bg-gray-50 rounded-lg p-4 space-y-2 text-sm\">\n                <div className=\"flex justify-between\">\n                  <span className=\"text-gray-600\">订单号:</span>\n                  <span className=\"font-mono\">{orderData.orderId}</span>\n                </div>\n                <div className=\"flex justify-between\">\n                  <span className=\"text-gray-600\">邮箱:</span>\n                  <span>{orderData.email}</span>\n                </div>\n                <div className=\"flex justify-between\">\n                  <span className=\"text-gray-600\">总金额:</span>\n                  <span className=\"font-bold text-blue-600\">${orderData.totalAmount.toFixed(2)}</span>\n                </div>\n                <div className=\"flex justify-between\">\n                  <span className=\"text-gray-600\">购买时间:</span>\n                  <span>{new Date(orderData.createdAt).toLocaleString('zh-CN')}</span>\n                </div>\n              </div>\n            </div>\n\n            {/* 卡密信息 */}\n            <div className=\"space-y-6\">\n              {Object.entries(orderData.cards).map(([productName, cards]) => (\n                <div key={productName} className=\"bg-white rounded-lg shadow p-6\">\n                  <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">{productName}</h3>\n                  <div className=\"space-y-3\">\n                    {cards.map((card, index) => (\n                      <div key={card.id} className=\"flex items-center justify-between bg-gray-50 rounded-lg p-3\">\n                        <div className=\"flex-1\">\n                          <div className=\"text-sm text-gray-600 mb-1\">卡密 #{index + 1}</div>\n                          <div className=\"font-mono text-lg text-gray-900 break-all\">\n                            {card.cardData}\n                          </div>\n                        </div>\n                        <Button\n                          onClick={() => copyToClipboard(card.cardData, card.id)}\n                          variant=\"outline\"\n                          size=\"sm\"\n                          className=\"ml-3\"\n                        >\n                          <Copy className=\"w-4 h-4 mr-1\" />\n                          {copiedCard === card.id ? '已复制' : '复制'}\n                        </Button>\n                      </div>\n                    ))}\n                  </div>\n                </div>\n              ))}\n            </div>\n\n            {/* 使用说明 */}\n            <div className=\"bg-blue-50 border border-blue-200 rounded-lg p-6 mt-6\">\n              <h3 className=\"text-sm font-medium text-blue-900 mb-2\">重要提示</h3>\n              <ul className=\"text-sm text-blue-800 space-y-1\">\n                <li>• 请妥善保管您的卡密信息，避免泄露给他人</li>\n                <li>• 卡密一旦使用后无法退换，请确认后再使用</li>\n                <li>• 建议立即下载保存卡密信息以备后用</li>\n                <li>• 如有任何问题，请联系客服</li>\n                <li>• 您也可以通过订单查询页面重新查看卡密</li>\n              </ul>\n            </div>\n\n            {/* 操作按钮 */}\n            <div className=\"flex justify-center space-x-4 mt-8\">\n              <Link href=\"/\">\n                <Button>\n                  <Home className=\"w-4 h-4 mr-2\" />\n                  返回首页\n                </Button>\n              </Link>\n              <Link href=\"/orders\">\n                <Button variant=\"outline\">\n                  查看我的订单\n                </Button>\n              </Link>\n            </div>\n          </>\n        )}\n      </main>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AACA;AANA;;;;;;;AAqBe,SAAS;IACtB,MAAM,eAAe,CAAA,GAAA,kIAAA,CAAA,kBAAe,AAAD;IACnC,MAAM,YAAY,aAAa,GAAG,CAAC;IAEnC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAoB;IAC7D,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,WAAW;YACb;QACF,OAAO;YACL,SAAS;YACT,WAAW;QACb;IACF,GAAG;QAAC;KAAU;IAEd,MAAM,iBAAiB;QACrB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,CAAC,gCAAgC,EAAE,WAAW;YAC3E,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,SAAS,EAAE,EAAE;gBACf,aAAa;YACf,OAAO;gBACL,SAAS,KAAK,KAAK,IAAI;YACzB;QACF,EAAE,OAAO,OAAO;YACd,SAAS;QACX,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,kBAAkB,OAAO,MAAc;QAC3C,IAAI;YACF,MAAM,UAAU,SAAS,CAAC,SAAS,CAAC;YACpC,cAAc;YACd,WAAW,IAAM,cAAc,KAAK;QACtC,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,SAAS;QACzB;IACF;IAEA,MAAM,gBAAgB;QACpB,IAAI,CAAC,WAAW;QAEhB,IAAI,UAAU,CAAC,KAAK,EAAE,UAAU,OAAO,CAAC,EAAE,CAAC;QAC3C,WAAW,CAAC,IAAI,EAAE,UAAU,KAAK,CAAC,EAAE,CAAC;QACrC,WAAW,CAAC,MAAM,EAAE,UAAU,WAAW,CAAC,OAAO,CAAC,GAAG,EAAE,CAAC;QACxD,WAAW,CAAC,MAAM,EAAE,IAAI,KAAK,UAAU,SAAS,EAAE,cAAc,CAAC,SAAS,IAAI,CAAC;QAE/E,OAAO,OAAO,CAAC,UAAU,KAAK,EAAE,OAAO,CAAC,CAAC,CAAC,aAAa,MAAM;YAC3D,WAAW,CAAC,IAAI,EAAE,YAAY,MAAM,CAAC;YACrC,MAAM,OAAO,CAAC,CAAC,MAAM;gBACnB,WAAW,GAAG,QAAQ,EAAE,EAAE,EAAE,KAAK,QAAQ,CAAC,EAAE,CAAC;YAC/C;YACA,WAAW;QACb;QAEA,MAAM,OAAO,IAAI,KAAK;YAAC;SAAQ,EAAE;YAAE,MAAM;QAA2B;QACpE,MAAM,MAAM,IAAI,eAAe,CAAC;QAChC,MAAM,IAAI,SAAS,aAAa,CAAC;QACjC,EAAE,IAAI,GAAG;QACT,EAAE,QAAQ,GAAG,CAAC,MAAM,EAAE,UAAU,OAAO,CAAC,UAAU,CAAC;QACnD,SAAS,IAAI,CAAC,WAAW,CAAC;QAC1B,EAAE,KAAK;QACP,SAAS,IAAI,CAAC,WAAW,CAAC;QAC1B,IAAI,eAAe,CAAC;IACtB;IAEA,IAAI,SAAS;QACX,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;0BAAgB;;;;;;;;;;;IAGrC;IAEA,IAAI,OAAO;QACT,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCAAqB;;;;;;kCACpC,8OAAC,4JAAA,CAAA,UAAI;wBAAC,MAAK;kCACT,cAAA,8OAAC,kIAAA,CAAA,SAAM;4BAAC,SAAQ;;8CACd,8OAAC,mMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;gCAAiB;;;;;;;;;;;;;;;;;;;;;;;IAO7C;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAI,WAAU;8CAAkC;;;;;;;;;;;0CAI7D,8OAAC;gCAAI,WAAU;0CAAwB;;;;;;;;;;;;;;;;;;;;;;0BAO7C,8OAAC;gBAAK,WAAU;;kCAEd,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,2NAAA,CAAA,cAAW;oCAAC,WAAU;;;;;;;;;;;0CAEzB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAAwC;;;;;;kDACtD,8OAAC;wCAAE,WAAU;kDAAgB;;;;;;;;;;;;;;;;;;oBAIhC,2BACC;;0CAEE,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DAAsC;;;;;;0DACpD,8OAAC,kIAAA,CAAA,SAAM;gDAAC,SAAS;gDAAe,SAAQ;;kEACtC,8OAAC,0MAAA,CAAA,WAAQ;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;;;;;;;kDAKzC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAK,WAAU;kEAAgB;;;;;;kEAChC,8OAAC;wDAAK,WAAU;kEAAa,UAAU,OAAO;;;;;;;;;;;;0DAEhD,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAK,WAAU;kEAAgB;;;;;;kEAChC,8OAAC;kEAAM,UAAU,KAAK;;;;;;;;;;;;0DAExB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAK,WAAU;kEAAgB;;;;;;kEAChC,8OAAC;wDAAK,WAAU;;4DAA0B;4DAAE,UAAU,WAAW,CAAC,OAAO,CAAC;;;;;;;;;;;;;0DAE5E,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAK,WAAU;kEAAgB;;;;;;kEAChC,8OAAC;kEAAM,IAAI,KAAK,UAAU,SAAS,EAAE,cAAc,CAAC;;;;;;;;;;;;;;;;;;;;;;;;0CAM1D,8OAAC;gCAAI,WAAU;0CACZ,OAAO,OAAO,CAAC,UAAU,KAAK,EAAE,GAAG,CAAC,CAAC,CAAC,aAAa,MAAM,iBACxD,8OAAC;wCAAsB,WAAU;;0DAC/B,8OAAC;gDAAG,WAAU;0DAA4C;;;;;;0DAC1D,8OAAC;gDAAI,WAAU;0DACZ,MAAM,GAAG,CAAC,CAAC,MAAM,sBAChB,8OAAC;wDAAkB,WAAU;;0EAC3B,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAI,WAAU;;4EAA6B;4EAAK,QAAQ;;;;;;;kFACzD,8OAAC;wEAAI,WAAU;kFACZ,KAAK,QAAQ;;;;;;;;;;;;0EAGlB,8OAAC,kIAAA,CAAA,SAAM;gEACL,SAAS,IAAM,gBAAgB,KAAK,QAAQ,EAAE,KAAK,EAAE;gEACrD,SAAQ;gEACR,MAAK;gEACL,WAAU;;kFAEV,8OAAC,kMAAA,CAAA,OAAI;wEAAC,WAAU;;;;;;oEACf,eAAe,KAAK,EAAE,GAAG,QAAQ;;;;;;;;uDAd5B,KAAK,EAAE;;;;;;;;;;;uCAJb;;;;;;;;;;0CA4Bd,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAAyC;;;;;;kDACvD,8OAAC;wCAAG,WAAU;;0DACZ,8OAAC;0DAAG;;;;;;0DACJ,8OAAC;0DAAG;;;;;;0DACJ,8OAAC;0DAAG;;;;;;0DACJ,8OAAC;0DAAG;;;;;;0DACJ,8OAAC;0DAAG;;;;;;;;;;;;;;;;;;0CAKR,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;kDACT,cAAA,8OAAC,kIAAA,CAAA,SAAM;;8DACL,8OAAC,mMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;;;;;;kDAIrC,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;kDACT,cAAA,8OAAC,kIAAA,CAAA,SAAM;4CAAC,SAAQ;sDAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAU1C", "debugId": null}}]}