{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 68, "column": 0}, "map": {"version": 3, "sources": ["file:///F:/zidongfaka/auto-card-shop/src/lib/prisma.ts"], "sourcesContent": ["import { PrismaClient } from '@prisma/client'\n\nconst globalForPrisma = globalThis as unknown as {\n  prisma: PrismaClient | undefined\n}\n\nexport const prisma = globalForPrisma.prisma ?? new PrismaClient()\n\nif (process.env.NODE_ENV !== 'production') globalForPrisma.prisma = prisma\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,kBAAkB;AAIjB,MAAM,SAAS,gBAAgB,MAAM,IAAI,IAAI,6HAAA,CAAA,eAAY;AAEhE,wCAA2C,gBAAgB,MAAM,GAAG", "debugId": null}}, {"offset": {"line": 82, "column": 0}, "map": {"version": 3, "sources": ["file:///F:/zidongfaka/auto-card-shop/src/app/api/products/%5BproductId%5D/public/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server'\nimport { prisma } from '@/lib/prisma'\n\n// 获取单个商品信息（公开接口）\nexport async function GET(\n  request: NextRequest,\n  { params }: { params: Promise<{ productId: string }> }\n) {\n  try {\n    const resolvedParams = await params\n    const { productId } = resolvedParams\n\n    const product = await prisma.product.findUnique({\n      where: { \n        id: productId,\n        status: 'ACTIVE' // 只返回上架的商品\n      },\n      include: {\n        category: {\n          select: {\n            name: true\n          }\n        },\n        _count: {\n          select: {\n            cards: {\n              where: {\n                status: 'AVAILABLE'\n              }\n            }\n          }\n        }\n      }\n    })\n\n    if (!product) {\n      return NextResponse.json(\n        { error: '商品不存在或已下架' },\n        { status: 404 }\n      )\n    }\n\n    return NextResponse.json(product)\n  } catch (error) {\n    console.error('获取商品错误:', error)\n    return NextResponse.json(\n      { error: '服务器错误' },\n      { status: 500 }\n    )\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAGO,eAAe,IACpB,OAAoB,EACpB,EAAE,MAAM,EAA8C;IAEtD,IAAI;QACF,MAAM,iBAAiB,MAAM;QAC7B,MAAM,EAAE,SAAS,EAAE,GAAG;QAEtB,MAAM,UAAU,MAAM,sHAAA,CAAA,SAAM,CAAC,OAAO,CAAC,UAAU,CAAC;YAC9C,OAAO;gBACL,IAAI;gBACJ,QAAQ,SAAS,WAAW;YAC9B;YACA,SAAS;gBACP,UAAU;oBACR,QAAQ;wBACN,MAAM;oBACR;gBACF;gBACA,QAAQ;oBACN,QAAQ;wBACN,OAAO;4BACL,OAAO;gCACL,QAAQ;4BACV;wBACF;oBACF;gBACF;YACF;QACF;QAEA,IAAI,CAAC,SAAS;YACZ,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAY,GACrB;gBAAE,QAAQ;YAAI;QAElB;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;IAC3B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,WAAW;QACzB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAQ,GACjB;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}