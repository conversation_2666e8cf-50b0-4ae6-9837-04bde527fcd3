{"name": "auto-card-shop", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "db:seed": "tsx prisma/seed.ts", "db:reset": "prisma migrate reset && npm run db:seed"}, "prisma": {"seed": "tsx prisma/seed.ts"}, "dependencies": {"@auth/prisma-adapter": "^2.10.0", "@prisma/client": "^6.10.1", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-toast": "^1.2.14", "@stripe/stripe-js": "^7.4.0", "@types/bcryptjs": "^2.4.6", "@types/jsonwebtoken": "^9.0.10", "autoprefixer": "^10.4.21", "bcryptjs": "^3.0.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "jsonwebtoken": "^9.0.2", "lightningcss-win32-x64-msvc": "^1.30.1", "lucide-react": "^0.525.0", "next": "15.3.4", "next-auth": "^4.24.11", "postcss": "^8.5.6", "prisma": "^6.10.1", "react": "^19.0.0", "react-dom": "^19.0.0", "stripe": "^18.2.1", "tailwind-merge": "^3.3.1", "tailwindcss": "^3.4.0"}, "devDependencies": {"@eslint/eslintrc": "^3", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.4", "tsx": "^4.20.3", "typescript": "^5"}}