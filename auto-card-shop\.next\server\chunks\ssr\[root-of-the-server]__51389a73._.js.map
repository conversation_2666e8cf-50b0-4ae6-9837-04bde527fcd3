{"version": 3, "sources": [], "sections": [{"offset": {"line": 31, "column": 0}, "map": {"version": 3, "sources": ["file:///F:/zidongfaka/auto-card-shop/src/app/admin/layout.tsx"], "sourcesContent": ["'use client'\n\nimport { useSession } from 'next-auth/react'\nimport { useRouter } from 'next/navigation'\nimport { useEffect } from 'react'\nimport Link from 'next/link'\nimport {\n  Package,\n  ShoppingCart,\n  CreditCard,\n  Users,\n  Settings,\n  Home,\n  Folder\n} from 'lucide-react'\n\nexport default function AdminLayout({\n  children,\n}: {\n  children: React.ReactNode\n}) {\n  const { data: session, status } = useSession()\n  const router = useRouter()\n\n  useEffect(() => {\n    if (status === 'loading') return\n    \n    if (!session) {\n      router.push('/auth/signin')\n      return\n    }\n\n    if (session.user.role !== 'ADMIN') {\n      router.push('/')\n      return\n    }\n  }, [session, status, router])\n\n  if (status === 'loading') {\n    return <div className=\"flex items-center justify-center min-h-screen\">加载中...</div>\n  }\n\n  if (!session || session.user.role !== 'ADMIN') {\n    return null\n  }\n\n  const navigation = [\n    { name: '首页', href: '/', icon: Home },\n    { name: '仪表板', href: '/admin', icon: Home },\n    { name: '分类管理', href: '/admin/categories', icon: Folder },\n    { name: '商品管理', href: '/admin/products', icon: Package },\n    { name: '订单管理', href: '/admin/orders', icon: ShoppingCart },\n    { name: '卡密管理', href: '/admin/cards', icon: CreditCard },\n    { name: '用户管理', href: '/admin/users', icon: Users },\n    { name: '设置', href: '/admin/settings', icon: Settings },\n  ]\n\n  return (\n    <div className=\"min-h-screen bg-gray-100\">\n      <div className=\"flex\">\n        {/* 侧边栏 */}\n        <div className=\"w-64 bg-white shadow-md\">\n          <div className=\"p-6\">\n            <h1 className=\"text-xl font-bold text-gray-800\">管理后台</h1>\n          </div>\n          <nav className=\"mt-6\">\n            {navigation.map((item) => {\n              const Icon = item.icon\n              return (\n                <Link\n                  key={item.name}\n                  href={item.href}\n                  className=\"flex items-center px-6 py-3 text-gray-700 hover:bg-gray-50 hover:text-gray-900\"\n                >\n                  <Icon className=\"w-5 h-5 mr-3\" />\n                  {item.name}\n                </Link>\n              )\n            })}\n          </nav>\n        </div>\n\n        {/* 主内容区 */}\n        <div className=\"flex-1\">\n          <header className=\"bg-white shadow-sm border-b\">\n            <div className=\"px-6 py-4\">\n              <div className=\"flex items-center justify-between\">\n                <h2 className=\"text-lg font-semibold text-gray-800\">\n                  欢迎, {session.user.username}\n                </h2>\n                <div className=\"text-sm text-gray-600\">\n                  {session.user.email}\n                </div>\n              </div>\n            </div>\n          </header>\n          \n          <main className=\"p-6\">\n            {children}\n          </main>\n        </div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AANA;;;;;;;AAgBe,SAAS,YAAY,EAClC,QAAQ,EAGT;IACC,MAAM,EAAE,MAAM,OAAO,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,8IAAA,CAAA,aAAU,AAAD;IAC3C,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IAEvB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,WAAW,WAAW;QAE1B,IAAI,CAAC,SAAS;YACZ,OAAO,IAAI,CAAC;YACZ;QACF;QAEA,IAAI,QAAQ,IAAI,CAAC,IAAI,KAAK,SAAS;YACjC,OAAO,IAAI,CAAC;YACZ;QACF;IACF,GAAG;QAAC;QAAS;QAAQ;KAAO;IAE5B,IAAI,WAAW,WAAW;QACxB,qBAAO,8OAAC;YAAI,WAAU;sBAAgD;;;;;;IACxE;IAEA,IAAI,CAAC,WAAW,QAAQ,IAAI,CAAC,IAAI,KAAK,SAAS;QAC7C,OAAO;IACT;IAEA,MAAM,aAAa;QACjB;YAAE,MAAM;YAAM,MAAM;YAAK,MAAM,mMAAA,CAAA,OAAI;QAAC;QACpC;YAAE,MAAM;YAAO,MAAM;YAAU,MAAM,mMAAA,CAAA,OAAI;QAAC;QAC1C;YAAE,MAAM;YAAQ,MAAM;YAAqB,MAAM,sMAAA,CAAA,SAAM;QAAC;QACxD;YAAE,MAAM;YAAQ,MAAM;YAAmB,MAAM,wMAAA,CAAA,UAAO;QAAC;QACvD;YAAE,MAAM;YAAQ,MAAM;YAAiB,MAAM,sNAAA,CAAA,eAAY;QAAC;QAC1D;YAAE,MAAM;YAAQ,MAAM;YAAgB,MAAM,kNAAA,CAAA,aAAU;QAAC;QACvD;YAAE,MAAM;YAAQ,MAAM;YAAgB,MAAM,oMAAA,CAAA,QAAK;QAAC;QAClD;YAAE,MAAM;YAAM,MAAM;YAAmB,MAAM,0MAAA,CAAA,WAAQ;QAAC;KACvD;IAED,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAG,WAAU;0CAAkC;;;;;;;;;;;sCAElD,8OAAC;4BAAI,WAAU;sCACZ,WAAW,GAAG,CAAC,CAAC;gCACf,MAAM,OAAO,KAAK,IAAI;gCACtB,qBACE,8OAAC,4JAAA,CAAA,UAAI;oCAEH,MAAM,KAAK,IAAI;oCACf,WAAU;;sDAEV,8OAAC;4CAAK,WAAU;;;;;;wCACf,KAAK,IAAI;;mCALL,KAAK,IAAI;;;;;4BAQpB;;;;;;;;;;;;8BAKJ,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAO,WAAU;sCAChB,cAAA,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;;gDAAsC;gDAC7C,QAAQ,IAAI,CAAC,QAAQ;;;;;;;sDAE5B,8OAAC;4CAAI,WAAU;sDACZ,QAAQ,IAAI,CAAC,KAAK;;;;;;;;;;;;;;;;;;;;;;sCAM3B,8OAAC;4BAAK,WAAU;sCACb;;;;;;;;;;;;;;;;;;;;;;;AAMb", "debugId": null}}]}