import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'

// 获取单个商品信息（公开接口）
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ productId: string }> }
) {
  try {
    const resolvedParams = await params
    const { productId } = resolvedParams

    const product = await prisma.product.findUnique({
      where: { 
        id: productId,
        status: 'ACTIVE' // 只返回上架的商品
      },
      include: {
        category: {
          select: {
            name: true
          }
        },
        _count: {
          select: {
            cards: {
              where: {
                status: 'AVAILABLE'
              }
            }
          }
        }
      }
    })

    if (!product) {
      return NextResponse.json(
        { error: '商品不存在或已下架' },
        { status: 404 }
      )
    }

    return NextResponse.json(product)
  } catch (error) {
    console.error('获取商品错误:', error)
    return NextResponse.json(
      { error: '服务器错误' },
      { status: 500 }
    )
  }
}
