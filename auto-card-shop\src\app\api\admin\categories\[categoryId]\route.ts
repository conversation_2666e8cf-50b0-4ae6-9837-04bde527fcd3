import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'

// 更新分类（仅管理员）
export async function PUT(
  request: NextRequest,
  { params }: { params: { categoryId: string } }
) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session || session.user.role !== 'ADMIN') {
      return NextResponse.json(
        { error: '权限不足' },
        { status: 403 }
      )
    }

    const { categoryId } = params
    const { name, description, slug, isActive } = await request.json()

    if (!name || !slug) {
      return NextResponse.json(
        { error: '名称和标识符是必需的' },
        { status: 400 }
      )
    }

    // 检查标识符是否已被其他分类使用
    const existingCategory = await prisma.category.findFirst({
      where: {
        slug,
        id: { not: categoryId }
      }
    })

    if (existingCategory) {
      return NextResponse.json(
        { error: '标识符已存在' },
        { status: 400 }
      )
    }

    const category = await prisma.category.update({
      where: { id: categoryId },
      data: {
        name,
        description,
        slug,
        isActive,
      },
      include: {
        _count: {
          select: {
            products: true
          }
        }
      }
    })

    return NextResponse.json(category)
  } catch (error) {
    console.error('更新分类错误:', error)
    return NextResponse.json(
      { error: '服务器错误' },
      { status: 500 }
    )
  }
}

// 删除分类（仅管理员）
export async function DELETE(
  request: NextRequest,
  { params }: { params: { categoryId: string } }
) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session || session.user.role !== 'ADMIN') {
      return NextResponse.json(
        { error: '权限不足' },
        { status: 403 }
      )
    }

    const { categoryId } = params

    // 检查分类是否存在
    const category = await prisma.category.findUnique({
      where: { id: categoryId },
      include: {
        _count: {
          select: {
            products: true
          }
        }
      }
    })

    if (!category) {
      return NextResponse.json(
        { error: '分类不存在' },
        { status: 404 }
      )
    }

    // 如果有关联的商品，不允许删除
    if (category._count.products > 0) {
      return NextResponse.json(
        { error: '该分类下还有商品，不能删除' },
        { status: 400 }
      )
    }

    // 删除分类
    await prisma.category.delete({
      where: { id: categoryId }
    })

    return NextResponse.json({ message: '分类删除成功' })
  } catch (error) {
    console.error('删除分类错误:', error)
    return NextResponse.json(
      { error: '服务器错误' },
      { status: 500 }
    )
  }
}
