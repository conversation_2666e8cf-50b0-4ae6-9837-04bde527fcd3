{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///F:/zidongfaka/auto-card-shop/src/app/admin/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useEffect, useState } from 'react'\nimport { Package, ShoppingCart, CreditCard, Users } from 'lucide-react'\n\ninterface Stats {\n  totalProducts: number\n  totalOrders: number\n  totalCards: number\n  totalUsers: number\n  recentOrders: any[]\n}\n\nexport default function AdminDashboard() {\n  const [stats, setStats] = useState<Stats>({\n    totalProducts: 0,\n    totalOrders: 0,\n    totalCards: 0,\n    totalUsers: 0,\n    recentOrders: []\n  })\n  const [loading, setLoading] = useState(true)\n\n  useEffect(() => {\n    fetchStats()\n  }, [])\n\n  const fetchStats = async () => {\n    try {\n      // 这里可以创建一个专门的统计 API\n      // 暂时使用模拟数据\n      setStats({\n        totalProducts: 4,\n        totalOrders: 0,\n        totalCards: 20,\n        totalUsers: 1,\n        recentOrders: []\n      })\n    } catch (error) {\n      console.error('获取统计数据失败:', error)\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const statCards = [\n    {\n      title: '商品总数',\n      value: stats.totalProducts,\n      icon: Package,\n      color: 'bg-blue-500'\n    },\n    {\n      title: '订单总数',\n      value: stats.totalOrders,\n      icon: ShoppingCart,\n      color: 'bg-green-500'\n    },\n    {\n      title: '卡密总数',\n      value: stats.totalCards,\n      icon: CreditCard,\n      color: 'bg-purple-500'\n    },\n    {\n      title: '用户总数',\n      value: stats.totalUsers,\n      icon: Users,\n      color: 'bg-orange-500'\n    }\n  ]\n\n  if (loading) {\n    return (\n      <div className=\"flex items-center justify-center h-64\">\n        <div className=\"text-gray-500\">加载中...</div>\n      </div>\n    )\n  }\n\n  return (\n    <div className=\"space-y-6\">\n      <div>\n        <h1 className=\"text-2xl font-bold text-gray-900\">仪表板</h1>\n        <p className=\"text-gray-600\">欢迎来到管理后台</p>\n      </div>\n\n      {/* 统计卡片 */}\n      <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\">\n        {statCards.map((card, index) => {\n          const Icon = card.icon\n          return (\n            <div key={index} className=\"bg-white rounded-lg shadow p-6\">\n              <div className=\"flex items-center\">\n                <div className={`${card.color} rounded-lg p-3`}>\n                  <Icon className=\"w-6 h-6 text-white\" />\n                </div>\n                <div className=\"ml-4\">\n                  <p className=\"text-sm font-medium text-gray-600\">{card.title}</p>\n                  <p className=\"text-2xl font-bold text-gray-900\">{card.value}</p>\n                </div>\n              </div>\n            </div>\n          )\n        })}\n      </div>\n\n      {/* 快速操作 */}\n      <div className=\"bg-white rounded-lg shadow p-6\">\n        <h2 className=\"text-lg font-semibold text-gray-900 mb-4\">快速操作</h2>\n        <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\n          <a\n            href=\"/admin/products\"\n            className=\"p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors\"\n          >\n            <Package className=\"w-8 h-8 text-blue-500 mb-2\" />\n            <h3 className=\"font-medium text-gray-900\">管理商品</h3>\n            <p className=\"text-sm text-gray-600\">添加、编辑或删除商品</p>\n          </a>\n          \n          <a\n            href=\"/admin/cards\"\n            className=\"p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors\"\n          >\n            <CreditCard className=\"w-8 h-8 text-purple-500 mb-2\" />\n            <h3 className=\"font-medium text-gray-900\">管理卡密</h3>\n            <p className=\"text-sm text-gray-600\">批量添加或查看卡密</p>\n          </a>\n          \n          <a\n            href=\"/admin/orders\"\n            className=\"p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors\"\n          >\n            <ShoppingCart className=\"w-8 h-8 text-green-500 mb-2\" />\n            <h3 className=\"font-medium text-gray-900\">查看订单</h3>\n            <p className=\"text-sm text-gray-600\">管理和处理订单</p>\n          </a>\n        </div>\n      </div>\n\n      {/* 系统信息 */}\n      <div className=\"bg-white rounded-lg shadow p-6\">\n        <h2 className=\"text-lg font-semibold text-gray-900 mb-4\">系统信息</h2>\n        <div className=\"space-y-2 text-sm\">\n          <div className=\"flex justify-between\">\n            <span className=\"text-gray-600\">系统版本:</span>\n            <span className=\"text-gray-900\">v1.0.0</span>\n          </div>\n          <div className=\"flex justify-between\">\n            <span className=\"text-gray-600\">数据库:</span>\n            <span className=\"text-gray-900\">SQLite</span>\n          </div>\n          <div className=\"flex justify-between\">\n            <span className=\"text-gray-600\">支付网关:</span>\n            <span className=\"text-gray-900\">Stripe</span>\n          </div>\n        </div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;;;AAHA;;;AAae,SAAS;;IACtB,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAS;QACxC,eAAe;QACf,aAAa;QACb,YAAY;QACZ,YAAY;QACZ,cAAc,EAAE;IAClB;IACA,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;oCAAE;YACR;QACF;mCAAG,EAAE;IAEL,MAAM,aAAa;QACjB,IAAI;YACF,oBAAoB;YACpB,WAAW;YACX,SAAS;gBACP,eAAe;gBACf,aAAa;gBACb,YAAY;gBACZ,YAAY;gBACZ,cAAc,EAAE;YAClB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,aAAa;QAC7B,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,YAAY;QAChB;YACE,OAAO;YACP,OAAO,MAAM,aAAa;YAC1B,MAAM,2MAAA,CAAA,UAAO;YACb,OAAO;QACT;QACA;YACE,OAAO;YACP,OAAO,MAAM,WAAW;YACxB,MAAM,yNAAA,CAAA,eAAY;YAClB,OAAO;QACT;QACA;YACE,OAAO;YACP,OAAO,MAAM,UAAU;YACvB,MAAM,qNAAA,CAAA,aAAU;YAChB,OAAO;QACT;QACA;YACE,OAAO;YACP,OAAO,MAAM,UAAU;YACvB,MAAM,uMAAA,CAAA,QAAK;YACX,OAAO;QACT;KACD;IAED,IAAI,SAAS;QACX,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;0BAAgB;;;;;;;;;;;IAGrC;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;;kCACC,6LAAC;wBAAG,WAAU;kCAAmC;;;;;;kCACjD,6LAAC;wBAAE,WAAU;kCAAgB;;;;;;;;;;;;0BAI/B,6LAAC;gBAAI,WAAU;0BACZ,UAAU,GAAG,CAAC,CAAC,MAAM;oBACpB,MAAM,OAAO,KAAK,IAAI;oBACtB,qBACE,6LAAC;wBAAgB,WAAU;kCACzB,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAW,GAAG,KAAK,KAAK,CAAC,eAAe,CAAC;8CAC5C,cAAA,6LAAC;wCAAK,WAAU;;;;;;;;;;;8CAElB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAE,WAAU;sDAAqC,KAAK,KAAK;;;;;;sDAC5D,6LAAC;4CAAE,WAAU;sDAAoC,KAAK,KAAK;;;;;;;;;;;;;;;;;;uBAPvD;;;;;gBAYd;;;;;;0BAIF,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAA2C;;;;;;kCACzD,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCACC,MAAK;gCACL,WAAU;;kDAEV,6LAAC,2MAAA,CAAA,UAAO;wCAAC,WAAU;;;;;;kDACnB,6LAAC;wCAAG,WAAU;kDAA4B;;;;;;kDAC1C,6LAAC;wCAAE,WAAU;kDAAwB;;;;;;;;;;;;0CAGvC,6LAAC;gCACC,MAAK;gCACL,WAAU;;kDAEV,6LAAC,qNAAA,CAAA,aAAU;wCAAC,WAAU;;;;;;kDACtB,6LAAC;wCAAG,WAAU;kDAA4B;;;;;;kDAC1C,6LAAC;wCAAE,WAAU;kDAAwB;;;;;;;;;;;;0CAGvC,6LAAC;gCACC,MAAK;gCACL,WAAU;;kDAEV,6LAAC,yNAAA,CAAA,eAAY;wCAAC,WAAU;;;;;;kDACxB,6LAAC;wCAAG,WAAU;kDAA4B;;;;;;kDAC1C,6LAAC;wCAAE,WAAU;kDAAwB;;;;;;;;;;;;;;;;;;;;;;;;0BAM3C,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAA2C;;;;;;kCACzD,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAK,WAAU;kDAAgB;;;;;;kDAChC,6LAAC;wCAAK,WAAU;kDAAgB;;;;;;;;;;;;0CAElC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAK,WAAU;kDAAgB;;;;;;kDAChC,6LAAC;wCAAK,WAAU;kDAAgB;;;;;;;;;;;;0CAElC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAK,WAAU;kDAAgB;;;;;;kDAChC,6LAAC;wCAAK,WAAU;kDAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAM5C;GAnJwB;KAAA", "debugId": null}}]}