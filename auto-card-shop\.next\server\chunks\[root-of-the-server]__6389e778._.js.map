{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 108, "column": 0}, "map": {"version": 3, "sources": ["file:///F:/zidongfaka/auto-card-shop/src/lib/stripe.ts"], "sourcesContent": ["import Stripe from 'stripe'\n\nexport const stripe = new Stripe(process.env.STRIPE_SECRET_KEY!, {\n  apiVersion: '2024-12-18.acacia',\n  typescript: true,\n})\n\nexport const getStripe = () => {\n  if (typeof window !== 'undefined') {\n    const { loadStripe } = require('@stripe/stripe-js')\n    return loadStripe(process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY!)\n  }\n  return null\n}\n"], "names": [], "mappings": ";;;;AAAA;;AAEO,MAAM,SAAS,IAAI,wJAAA,CAAA,UAAM,CAAC,QAAQ,GAAG,CAAC,iBAAiB,EAAG;IAC/D,YAAY;IACZ,YAAY;AACd;AAEO,MAAM,YAAY;IACvB,uCAAmC;;IAGnC;IACA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 138, "column": 0}, "map": {"version": 3, "sources": ["file:///F:/zidongfaka/auto-card-shop/src/lib/prisma.ts"], "sourcesContent": ["import { PrismaClient } from '@prisma/client'\n\nconst globalForPrisma = globalThis as unknown as {\n  prisma: PrismaClient | undefined\n}\n\nexport const prisma = globalForPrisma.prisma ?? new PrismaClient()\n\nif (process.env.NODE_ENV !== 'production') globalForPrisma.prisma = prisma\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,kBAAkB;AAIjB,MAAM,SAAS,gBAAgB,MAAM,IAAI,IAAI,6HAAA,CAAA,eAAY;AAEhE,wCAA2C,gBAAgB,MAAM,GAAG", "debugId": null}}, {"offset": {"line": 152, "column": 0}, "map": {"version": 3, "sources": ["file:///F:/zidongfaka/auto-card-shop/src/app/api/payment/success/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server'\nimport { stripe } from '@/lib/stripe'\nimport { prisma } from '@/lib/prisma'\n\nexport async function GET(request: NextRequest) {\n  try {\n    const { searchParams } = new URL(request.url)\n    const sessionId = searchParams.get('session_id')\n\n    if (!sessionId) {\n      return NextResponse.json(\n        { error: '缺少支付会话ID' },\n        { status: 400 }\n      )\n    }\n\n    // 从 Stripe 获取支付会话信息\n    const session = await stripe.checkout.sessions.retrieve(sessionId)\n\n    if (!session.metadata?.orderId) {\n      return NextResponse.json(\n        { error: '订单信息缺失' },\n        { status: 400 }\n      )\n    }\n\n    const orderId = session.metadata.orderId\n\n    // 获取订单和卡密信息\n    const order = await prisma.order.findUnique({\n      where: { id: orderId },\n      include: {\n        orderItems: {\n          include: {\n            product: true\n          }\n        }\n      }\n    })\n\n    if (!order) {\n      return NextResponse.json(\n        { error: '订单不存在' },\n        { status: 404 }\n      )\n    }\n\n    // 如果订单还未交付，先处理发卡\n    if (order.status === 'PAID') {\n      // 自动发卡逻辑\n      for (const orderItem of order.orderItems) {\n        // 获取可用的卡密\n        const availableCards = await prisma.card.findMany({\n          where: {\n            productId: orderItem.productId,\n            status: 'AVAILABLE'\n          },\n          take: orderItem.quantity,\n          orderBy: {\n            createdAt: 'asc'\n          }\n        })\n\n        if (availableCards.length >= orderItem.quantity) {\n          // 标记卡密为已售出\n          await prisma.card.updateMany({\n            where: {\n              id: {\n                in: availableCards.map(card => card.id)\n              }\n            },\n            data: {\n              status: 'SOLD',\n              orderId: order.id,\n              usedAt: new Date()\n            }\n          })\n\n          // 更新商品库存\n          await prisma.product.update({\n            where: { id: orderItem.productId },\n            data: {\n              stockCount: {\n                decrement: orderItem.quantity\n              }\n            }\n          })\n        }\n      }\n\n      // 更新订单状态为已交付\n      await prisma.order.update({\n        where: { id: orderId },\n        data: { status: 'DELIVERED' }\n      })\n    }\n\n    // 获取该订单的所有卡密\n    const cards = await prisma.card.findMany({\n      where: {\n        orderId: orderId,\n        status: 'SOLD'\n      },\n      include: {\n        product: {\n          select: {\n            name: true\n          }\n        }\n      },\n      orderBy: {\n        createdAt: 'asc'\n      }\n    })\n\n    // 按商品分组卡密\n    const cardsByProduct = cards.reduce((acc, card) => {\n      const productName = card.product.name\n      if (!acc[productName]) {\n        acc[productName] = []\n      }\n      acc[productName].push({\n        id: card.id,\n        cardData: card.cardData,\n        usedAt: card.usedAt\n      })\n      return acc\n    }, {} as Record<string, any[]>)\n\n    return NextResponse.json({\n      orderId: order.id,\n      email: order.email,\n      totalAmount: order.totalAmount,\n      status: order.status,\n      createdAt: order.createdAt,\n      cards: cardsByProduct\n    })\n  } catch (error) {\n    console.error('获取支付成功信息错误:', error)\n    return NextResponse.json(\n      { error: '服务器错误' },\n      { status: 500 }\n    )\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;;;;AAEO,eAAe,IAAI,OAAoB;IAC5C,IAAI;QACF,MAAM,EAAE,YAAY,EAAE,GAAG,IAAI,IAAI,QAAQ,GAAG;QAC5C,MAAM,YAAY,aAAa,GAAG,CAAC;QAEnC,IAAI,CAAC,WAAW;YACd,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAW,GACpB;gBAAE,QAAQ;YAAI;QAElB;QAEA,oBAAoB;QACpB,MAAM,UAAU,MAAM,sHAAA,CAAA,SAAM,CAAC,QAAQ,CAAC,QAAQ,CAAC,QAAQ,CAAC;QAExD,IAAI,CAAC,QAAQ,QAAQ,EAAE,SAAS;YAC9B,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAS,GAClB;gBAAE,QAAQ;YAAI;QAElB;QAEA,MAAM,UAAU,QAAQ,QAAQ,CAAC,OAAO;QAExC,YAAY;QACZ,MAAM,QAAQ,MAAM,sHAAA,CAAA,SAAM,CAAC,KAAK,CAAC,UAAU,CAAC;YAC1C,OAAO;gBAAE,IAAI;YAAQ;YACrB,SAAS;gBACP,YAAY;oBACV,SAAS;wBACP,SAAS;oBACX;gBACF;YACF;QACF;QAEA,IAAI,CAAC,OAAO;YACV,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAQ,GACjB;gBAAE,QAAQ;YAAI;QAElB;QAEA,iBAAiB;QACjB,IAAI,MAAM,MAAM,KAAK,QAAQ;YAC3B,SAAS;YACT,KAAK,MAAM,aAAa,MAAM,UAAU,CAAE;gBACxC,UAAU;gBACV,MAAM,iBAAiB,MAAM,sHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,QAAQ,CAAC;oBAChD,OAAO;wBACL,WAAW,UAAU,SAAS;wBAC9B,QAAQ;oBACV;oBACA,MAAM,UAAU,QAAQ;oBACxB,SAAS;wBACP,WAAW;oBACb;gBACF;gBAEA,IAAI,eAAe,MAAM,IAAI,UAAU,QAAQ,EAAE;oBAC/C,WAAW;oBACX,MAAM,sHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,UAAU,CAAC;wBAC3B,OAAO;4BACL,IAAI;gCACF,IAAI,eAAe,GAAG,CAAC,CAAA,OAAQ,KAAK,EAAE;4BACxC;wBACF;wBACA,MAAM;4BACJ,QAAQ;4BACR,SAAS,MAAM,EAAE;4BACjB,QAAQ,IAAI;wBACd;oBACF;oBAEA,SAAS;oBACT,MAAM,sHAAA,CAAA,SAAM,CAAC,OAAO,CAAC,MAAM,CAAC;wBAC1B,OAAO;4BAAE,IAAI,UAAU,SAAS;wBAAC;wBACjC,MAAM;4BACJ,YAAY;gCACV,WAAW,UAAU,QAAQ;4BAC/B;wBACF;oBACF;gBACF;YACF;YAEA,aAAa;YACb,MAAM,sHAAA,CAAA,SAAM,CAAC,KAAK,CAAC,MAAM,CAAC;gBACxB,OAAO;oBAAE,IAAI;gBAAQ;gBACrB,MAAM;oBAAE,QAAQ;gBAAY;YAC9B;QACF;QAEA,aAAa;QACb,MAAM,QAAQ,MAAM,sHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,QAAQ,CAAC;YACvC,OAAO;gBACL,SAAS;gBACT,QAAQ;YACV;YACA,SAAS;gBACP,SAAS;oBACP,QAAQ;wBACN,MAAM;oBACR;gBACF;YACF;YACA,SAAS;gBACP,WAAW;YACb;QACF;QAEA,UAAU;QACV,MAAM,iBAAiB,MAAM,MAAM,CAAC,CAAC,KAAK;YACxC,MAAM,cAAc,KAAK,OAAO,CAAC,IAAI;YACrC,IAAI,CAAC,GAAG,CAAC,YAAY,EAAE;gBACrB,GAAG,CAAC,YAAY,GAAG,EAAE;YACvB;YACA,GAAG,CAAC,YAAY,CAAC,IAAI,CAAC;gBACpB,IAAI,KAAK,EAAE;gBACX,UAAU,KAAK,QAAQ;gBACvB,QAAQ,KAAK,MAAM;YACrB;YACA,OAAO;QACT,GAAG,CAAC;QAEJ,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS,MAAM,EAAE;YACjB,OAAO,MAAM,KAAK;YAClB,aAAa,MAAM,WAAW;YAC9B,QAAQ,MAAM,MAAM;YACpB,WAAW,MAAM,SAAS;YAC1B,OAAO;QACT;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,eAAe;QAC7B,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAQ,GACjB;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}