{"/_not-found/page": "app/_not-found/page.js", "/admin/cards/page": "app/admin/cards/page.js", "/admin/categories/page": "app/admin/categories/page.js", "/admin/orders/page": "app/admin/orders/page.js", "/admin/page": "app/admin/page.js", "/admin/products/page": "app/admin/products/page.js", "/admin/settings/page": "app/admin/settings/page.js", "/admin/users/page": "app/admin/users/page.js", "/api/admin/categories/route": "app/api/admin/categories/route.js", "/api/admin/users/route": "app/api/admin/users/route.js", "/api/auth/[...nextauth]/route": "app/api/auth/[...nextauth]/route.js", "/api/cards/route": "app/api/cards/route.js", "/api/categories/route": "app/api/categories/route.js", "/api/orders/[orderId]/cards/route": "app/api/orders/[orderId]/cards/route.js", "/api/orders/route": "app/api/orders/route.js", "/api/payment/create-intent/route": "app/api/payment/create-intent/route.js", "/api/products/[productId]/public/route": "app/api/products/[productId]/public/route.js", "/api/products/route": "app/api/products/route.js", "/api/test-purchase/route": "app/api/test-purchase/route.js", "/auth/signin/page": "app/auth/signin/page.js", "/checkout/page": "app/checkout/page.js", "/favicon.ico/route": "app/favicon.ico/route.js", "/orders/[orderId]/cards/page": "app/orders/[orderId]/cards/page.js", "/orders/page": "app/orders/page.js", "/page": "app/page.js", "/product/[productId]/page": "app/product/[productId]/page.js", "/test-purchase/page": "app/test-purchase/page.js"}