'use client'

import { useState } from 'react'
import { Button } from '@/components/ui/button'
import { formatPrice } from '@/lib/utils'
import { ShoppingCart, CheckCircle } from 'lucide-react'
import Link from 'next/link'

export default function TestPurchasePage() {
  const [email, setEmail] = useState('')
  const [loading, setLoading] = useState(false)
  const [result, setResult] = useState<any>(null)
  const [error, setError] = useState('')

  const handleTestPurchase = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!email) {
      setError('请输入邮箱地址')
      return
    }

    setLoading(true)
    setError('')

    try {
      // 模拟购买第一个商品
      const response = await fetch('/api/test-purchase', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email,
          productId: 'cmcj0qg1h000qtwm4egouia83', // 使用第一个商品的ID
          quantity: 1
        }),
      })

      const data = await response.json()

      if (response.ok) {
        setResult(data)
      } else {
        setError(data.error || '购买失败')
      }
    } catch (error) {
      setError('购买失败，请重试')
    } finally {
      setLoading(false)
    }
  }

  if (result) {
    return (
      <div className="min-h-screen bg-gray-50">
        <nav className="bg-white shadow-sm border-b">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex justify-between items-center h-16">
              <div className="flex items-center">
                <Link href="/" className="text-xl font-bold text-gray-900">
                  自动发卡网站
                </Link>
              </div>
              <div className="text-sm text-gray-600">
                测试购买成功
              </div>
            </div>
          </div>
        </nav>

        <main className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="bg-white rounded-lg shadow p-6 mb-6">
            <div className="flex items-center justify-center mb-4">
              <CheckCircle className="w-16 h-16 text-green-500" />
            </div>
            <div className="text-center">
              <h1 className="text-2xl font-bold text-gray-900 mb-2">测试购买成功！</h1>
              <p className="text-gray-600">以下是您的卡密信息</p>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow p-6 mb-6">
            <h2 className="text-lg font-semibold text-gray-900 mb-4">订单信息</h2>
            <div className="bg-gray-50 rounded-lg p-4 space-y-2 text-sm">
              <div className="flex justify-between">
                <span className="text-gray-600">订单号:</span>
                <span className="font-mono">{result.orderId}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">邮箱:</span>
                <span>{result.email}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">总金额:</span>
                <span className="font-bold text-blue-600">${result.totalAmount.toFixed(2)}</span>
              </div>
            </div>
          </div>

          {result.cards && Object.keys(result.cards).length > 0 && (
            <div className="space-y-6">
              {Object.entries(result.cards).map(([productName, cards]: [string, any[]]) => (
                <div key={productName} className="bg-white rounded-lg shadow p-6">
                  <h3 className="text-lg font-semibold text-gray-900 mb-4">{productName}</h3>
                  <div className="space-y-3">
                    {cards.map((card, index) => (
                      <div key={card.id} className="bg-gray-50 rounded-lg p-3">
                        <div className="text-sm text-gray-600 mb-1">卡密 #{index + 1}</div>
                        <div className="font-mono text-lg text-gray-900 break-all">
                          {card.cardData}
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              ))}
            </div>
          )}

          <div className="flex justify-center space-x-4 mt-8">
            <Link href="/">
              <Button>返回首页</Button>
            </Link>
            <Link href="/orders">
              <Button variant="outline">查看订单</Button>
            </Link>
          </div>
        </main>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <nav className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <Link href="/" className="text-xl font-bold text-gray-900">
                自动发卡网站
              </Link>
            </div>
            <div className="text-sm text-gray-600">
              测试购买
            </div>
          </div>
        </div>
      </nav>

      <main className="max-w-2xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="bg-white rounded-lg shadow p-6">
          <h1 className="text-2xl font-bold text-gray-900 mb-6">测试购买功能</h1>
          
          <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-6">
            <h3 className="text-sm font-medium text-yellow-800 mb-2">测试说明</h3>
            <ul className="text-sm text-yellow-700 space-y-1">
              <li>• 这是一个测试页面，用于验证购买和发卡功能</li>
              <li>• 不会进行真实支付，直接模拟支付成功</li>
              <li>• 将自动购买第一个商品并发放卡密</li>
              <li>• 仅用于开发测试，生产环境请删除此页面</li>
            </ul>
          </div>

          <form onSubmit={handleTestPurchase} className="space-y-4">
            <div>
              <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-1">
                邮箱地址 *
              </label>
              <input
                id="email"
                type="email"
                required
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                placeholder="请输入邮箱地址"
              />
            </div>

            {error && (
              <div className="bg-red-50 border border-red-200 text-red-600 px-4 py-3 rounded-md">
                {error}
              </div>
            )}

            <div className="space-y-3">
              <Button
                type="submit"
                disabled={loading}
                className="w-full"
              >
                <ShoppingCart className="w-4 h-4 mr-2" />
                {loading ? '处理中...' : '测试购买'}
              </Button>
              
              <Link href="/">
                <Button variant="outline" className="w-full">
                  返回首页
                </Button>
              </Link>
            </div>
          </form>
        </div>
      </main>
    </div>
  )
}
